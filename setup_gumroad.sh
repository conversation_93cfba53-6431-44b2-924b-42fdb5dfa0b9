#!/bin/bash

# Gumroad Local Development Setup Script
# This script sets up the complete Gumroad development environment
# Supports both fresh setup and re-runs for development workflow

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Required versions
REQUIRED_RUBY_VERSION="3.4.3"
REQUIRED_NODE_VERSION="20.17.0"

# Configuration flags
FORCE_RESET=false
SKIP_DEPENDENCIES=false
SKIP_DOCKER=false
SKIP_DB_RESET=false
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force-reset)
            FORCE_RESET=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPENDENCIES=true
            shift
            ;;
        --skip-docker)
            SKIP_DOCKER=true
            shift
            ;;
        --skip-db-reset)
            SKIP_DB_RESET=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Gumroad Development Setup Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --force-reset     Reset everything (database, containers, etc.)"
            echo "  --skip-deps       Skip system dependency installation"
            echo "  --skip-docker     Skip Docker container setup"
            echo "  --skip-db-reset   Skip database reset (useful for preserving data)"
            echo "  --verbose         Enable verbose output"
            echo "  --help            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Full setup"
            echo "  $0 --skip-deps       # Quick setup, skip system dependencies"
            echo "  $0 --force-reset     # Complete reset and setup"
            echo "  $0 --skip-db-reset   # Setup without losing database data"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}🚀 Starting Gumroad Local Development Setup${NC}"
echo "=================================================="

if [[ "$FORCE_RESET" == "true" ]]; then
    echo -e "${PURPLE}[RESET MODE]${NC} Force reset enabled - will clean everything"
fi

# Function to print status messages
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_verbose() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${PURPLE}[VERBOSE]${NC} $1"
    fi
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to compare versions
version_compare() {
    if [[ $1 == $2 ]]; then
        return 0
    fi
    local IFS=.
    local i ver1=($1) ver2=($2)
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++)); do
        ver1[i]=0
    done
    for ((i=0; i<${#ver1[@]}; i++)); do
        if [[ -z ${ver2[i]} ]]; then
            ver2[i]=0
        fi
        if ((10#${ver1[i]} > 10#${ver2[i]})); then
            return 1
        fi
        if ((10#${ver1[i]} < 10#${ver2[i]})); then
            return 2
        fi
    done
    return 0
}

# Function to cleanup on exit
cleanup_on_exit() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        print_error "Setup failed with exit code $exit_code"
        print_status "You can re-run this script with different options:"
        print_status "  --skip-deps       Skip system dependency installation"
        print_status "  --skip-docker     Skip Docker container setup"
        print_status "  --skip-db-reset   Skip database reset"
        print_status "  --force-reset     Complete reset and setup"
    fi
}

trap cleanup_on_exit EXIT

# Function to check if Docker containers are running
check_docker_services() {
    local services=("mysql" "redis" "elasticsearch" "mongo")
    local all_running=true

    for service in "${services[@]}"; do
        if ! docker ps --format "table {{.Names}}" | grep -q "$service"; then
            all_running=false
            break
        fi
    done

    echo $all_running
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."

    while [[ $attempt -le $max_attempts ]]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            print_success "$service_name is ready"
            return 0
        fi

        print_verbose "Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 2
        ((attempt++))
    done

    print_error "$service_name failed to start within expected time"
    return 1
}

# Function to reset database completely
reset_database() {
    print_status "Resetting database completely..."

    # Drop and recreate database
    print_verbose "Dropping existing databases..."
    bin/rails runner "
    begin
      ActiveRecord::Base.connection.execute('DROP DATABASE IF EXISTS gumroad_development')
      ActiveRecord::Base.connection.execute('DROP DATABASE IF EXISTS gumroad_test')
      puts 'Databases dropped successfully'
    rescue => e
      puts 'Warning: Could not drop databases: ' + e.message
    end
    " 2>/dev/null || true

    # Create fresh databases
    print_verbose "Creating fresh databases..."
    bin/rails db:create

    # Run all migrations
    print_verbose "Running all migrations..."
    bin/rails db:migrate

    # Load schema
    print_verbose "Loading schema..."
    bin/rails db:schema:load

    # Seed database
    print_verbose "Seeding database..."
    bin/rails db:seed

    print_success "Database reset completed"
}

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS. Please refer to the README for other platforms."
    exit 1
fi

# Force reset cleanup
if [[ "$FORCE_RESET" == "true" ]]; then
    print_status "Performing force reset cleanup..."

    # Stop all Docker containers
    print_verbose "Stopping Docker containers..."
    COMPOSE_PROJECT_NAME=web docker compose -f docker/docker-compose-local.yml down --volumes --remove-orphans 2>/dev/null || true

    # Clean up Docker volumes
    print_verbose "Cleaning up Docker volumes..."
    docker volume prune -f 2>/dev/null || true

    # Clean up temporary files
    print_verbose "Cleaning up temporary files..."
    rm -rf tmp/cache/* tmp/pids/* log/*.log 2>/dev/null || true

    # Clean up node modules if requested
    if [[ "$SKIP_DEPENDENCIES" != "true" ]]; then
        print_verbose "Cleaning up node_modules..."
        rm -rf node_modules 2>/dev/null || true
    fi

    print_success "Force reset cleanup completed"
fi

# Skip system dependencies if requested
if [[ "$SKIP_DEPENDENCIES" == "true" ]]; then
    print_warning "Skipping system dependency installation"
else
    print_status "Checking system requirements..."

    # Check and install Homebrew
    if ! command_exists brew; then
        print_warning "Homebrew not found. Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

        # Add Homebrew to PATH for Apple Silicon Macs
        if [[ -f "/opt/homebrew/bin/brew" ]]; then
            echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
            eval "$(/opt/homebrew/bin/brew shellenv)"
        fi
    else
        print_success "Homebrew is installed"
    fi

    # Update Homebrew (ignore errors from broken taps)
    print_status "Updating Homebrew..."
    brew update || {
        print_warning "Homebrew update had some issues, but continuing..."
        print_status "Attempting to repair broken taps..."
        brew tap --repair || true
    }
fi

if [[ "$SKIP_DEPENDENCIES" != "true" ]]; then
    # Check and install rbenv for Ruby management
    if ! command_exists rbenv; then
        print_warning "rbenv not found. Installing rbenv..."
        brew install rbenv ruby-build

        # Add rbenv to shell profile
        if [[ -f ~/.zshrc ]]; then
            echo 'eval "$(rbenv init - zsh)"' >> ~/.zshrc
        elif [[ -f ~/.bash_profile ]]; then
            echo 'eval "$(rbenv init - bash)"' >> ~/.bash_profile
        fi

        # Initialize rbenv for current session
        eval "$(rbenv init -)"
    else
        print_success "rbenv is installed"
    fi

    # Check Ruby version
    print_status "Checking Ruby version..."

    # Initialize rbenv for current session
    eval "$(rbenv init -)"

    # Check if the required Ruby version is installed
    if rbenv versions | grep -q "$REQUIRED_RUBY_VERSION"; then
        print_success "Ruby $REQUIRED_RUBY_VERSION is already installed"
    else
        print_status "Installing Ruby $REQUIRED_RUBY_VERSION..."
        rbenv install $REQUIRED_RUBY_VERSION
    fi

    # Set the local Ruby version
    rbenv local $REQUIRED_RUBY_VERSION
    rbenv rehash

    # Verify the Ruby version is now correct
    CURRENT_RUBY=$(ruby -v | cut -d' ' -f2 | cut -d'p' -f1)
    if [[ "$CURRENT_RUBY" == "$REQUIRED_RUBY_VERSION" ]]; then
        print_success "Ruby $REQUIRED_RUBY_VERSION is now active"
    else
        print_error "Failed to activate Ruby $REQUIRED_RUBY_VERSION. Current: $CURRENT_RUBY"
        exit 1
    fi
else
    # Still need to initialize rbenv if skipping deps
    if command_exists rbenv; then
        eval "$(rbenv init -)"
        rbenv local $REQUIRED_RUBY_VERSION 2>/dev/null || true
        rbenv rehash 2>/dev/null || true
    fi
fi

if [[ "$SKIP_DEPENDENCIES" != "true" ]]; then
    # Install Bundler
    print_status "Installing/updating Bundler..."
    gem install bundler --conservative

    # Check Node.js version
    print_status "Checking Node.js version..."
    if command_exists node; then
        CURRENT_NODE=$(node -v | sed 's/v//')
        version_compare $CURRENT_NODE $REQUIRED_NODE_VERSION
        case $? in
            0) print_success "Node.js $CURRENT_NODE matches required version" ;;
            1) print_success "Node.js $CURRENT_NODE is newer than required $REQUIRED_NODE_VERSION" ;;
            2)
                print_warning "Node.js $CURRENT_NODE is older than required $REQUIRED_NODE_VERSION"
                print_status "Installing Node.js $REQUIRED_NODE_VERSION..."
                brew install node@20
                ;;
        esac
    else
        print_warning "Node.js not found. Installing Node.js..."
        brew install node@20
    fi

    # Install mkcert for SSL certificates
    if ! command_exists mkcert; then
        print_status "Installing mkcert for SSL certificates..."
        brew install mkcert
        brew install nss # for Firefox support
        mkcert -install
    else
        print_success "mkcert is installed"
    fi

    # Install netcat for service health checks
    if ! command_exists nc; then
        print_status "Installing netcat..."
        brew install netcat
    else
        print_success "netcat is installed"
    fi
fi

# Check Docker (always required)
print_status "Checking Docker..."
if ! command_exists docker; then
    print_error "Docker not found. Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
    print_error "After installing Docker Desktop, restart this script."
    exit 1
fi

# Check if Docker daemon is running
if ! docker ps >/dev/null 2>&1; then
    print_error "Docker daemon is not running. Please start Docker Desktop and try again."
    print_status "Waiting for Docker to start..."

    # Try to start Docker Desktop if it's installed
    if [[ -d "/Applications/Docker.app" ]]; then
        print_status "Attempting to start Docker Desktop..."
        open -a Docker

        # Wait for Docker to start (up to 60 seconds)
        for i in {1..60}; do
            if docker ps >/dev/null 2>&1; then
                print_success "Docker is now running"
                break
            fi
            echo -n "."
            sleep 1
        done
        echo ""

        if ! docker ps >/dev/null 2>&1; then
            print_error "Docker failed to start. Please start Docker Desktop manually and run this script again."
            exit 1
        fi
    else
        print_error "Please start Docker Desktop manually and run this script again."
        exit 1
    fi
else
    print_success "Docker is running"
fi

if [[ "$SKIP_DEPENDENCIES" != "true" ]]; then
    # Install system dependencies
    print_status "Installing system dependencies..."

    # MySQL and Percona Toolkit
    if ! command_exists mysql; then
        print_status "Installing MySQL..."
        brew install mysql@8.0 percona-toolkit
        brew link --force mysql@8.0

        # Configure MySQL build for Ruby gem
        if command_exists brew && [[ -d "$(brew --prefix openssl)" ]]; then
            bundle config --global build.mysql2 --with-opt-dir="$(brew --prefix openssl)"
        fi

        # Ensure MySQL is not running as a service
        brew services stop mysql@8.0 2>/dev/null || true
    else
        print_success "MySQL is installed"
    fi

    # Image processing libraries
    print_status "Installing image processing libraries..."
    brew install imagemagick libvips

    # FFmpeg for video processing
    if ! command_exists ffmpeg; then
        print_status "Installing FFmpeg..."
        brew install ffmpeg
    else
        print_success "FFmpeg is installed"
    fi

    # PDFtk for PDF processing
    if ! command_exists pdftk; then
        print_warning "PDFtk not found. You may need to install it manually from:"
        print_warning "https://www.pdflabs.com/tools/pdftk-the-pdf-toolkit/pdftk_server-2.02-mac_osx-10.11-setup.pkg"
    fi

    print_success "System dependencies installed successfully!"
fi

# Install Bundler and gems
print_status "Installing Ruby dependencies..."

# Install bundler
if ! command_exists bundle; then
    print_status "Installing Bundler..."
    gem install bundler
else
    print_success "Bundler is installed"
fi

# Install dotenv gem (required for some console commands)
gem install dotenv

# Configure Bundler
print_status "Configuring Bundler..."
bundle config --local without production staging

# Install gems
print_status "Installing Ruby gems (this may take a while)..."
bundle install

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."

# Enable corepack for npm version management
corepack enable

# Install npm dependencies
npm install

# Generate SSL certificates
print_status "Generating SSL certificates..."
if [[ ! -f "config/ssl_certificates/gumroad.dev.crt" ]]; then
    # Install the local CA
    mkcert -install

    # Generate certificates
    bin/generate_ssl_certificates
    print_success "SSL certificates generated"
else
    print_success "SSL certificates already exist"
fi

# Setup Docker services
if [[ "$SKIP_DOCKER" != "true" ]]; then
    print_status "Setting up Docker services..."
    print_warning "This will start MySQL, Redis, Elasticsearch, and MongoDB containers..."

    # Create necessary directories
    mkdir -p docker/tmp/elasticsearch
    mkdir -p docker/tmp/mongo
    mkdir -p docker/tmp/mysql
    mkdir -p docker/tmp/redis

    # Stop any existing containers if force reset
    if [[ "$FORCE_RESET" == "true" ]]; then
        print_status "Stopping any existing containers..."
        COMPOSE_PROJECT_NAME=web docker compose -f docker/docker-compose-local.yml down --volumes --remove-orphans 2>/dev/null || true
    else
        print_status "Stopping any existing containers..."
        COMPOSE_PROJECT_NAME=web docker compose -f docker/docker-compose-local.yml down --remove-orphans 2>/dev/null || true
    fi

    # Start Docker services in the background
    COMPOSE_PROJECT_NAME=web docker compose -f docker/docker-compose-local.yml up -d

    # Wait for services to be ready with improved health checks
    print_status "Waiting for services to start..."
    sleep 5

    # Wait for MySQL
    wait_for_service "MySQL" "127.0.0.1" "3306"

    # Wait for Redis
    wait_for_service "Redis" "127.0.0.1" "6379"

    # Wait for Elasticsearch
    wait_for_service "Elasticsearch" "127.0.0.1" "9200"

    # Wait for MongoDB
    wait_for_service "MongoDB" "127.0.0.1" "27017"

    print_success "All Docker services are ready"
else
    print_warning "Skipping Docker service setup"

    # Check if services are already running
    if [[ "$(check_docker_services)" == "false" ]]; then
        print_error "Docker services are not running and --skip-docker was specified."
        print_error "Please start Docker services manually or run without --skip-docker"
        exit 1
    else
        print_success "Docker services are already running"
    fi
fi

# Setup database
print_status "Setting up database..."

if [[ "$FORCE_RESET" == "true" ]] || [[ "$SKIP_DB_RESET" != "true" ]]; then
    if [[ "$FORCE_RESET" == "true" ]]; then
        print_status "Performing complete database reset..."
        reset_database
    else
        print_status "Setting up database (preserving existing data if possible)..."

        # Try gentle setup first
        if ! bin/rails db:prepare 2>/dev/null; then
            print_warning "Standard database setup failed, attempting reset..."
            reset_database
        else
            print_success "Database setup completed successfully"
        fi
    fi
else
    print_warning "Skipping database reset (--skip-db-reset specified)"

    # Still try to run migrations in case there are new ones
    print_status "Running any pending migrations..."
    if ! bin/rails db:migrate 2>/dev/null; then
        print_error "Migration failed. You may need to run without --skip-db-reset"
        print_error "Or manually resolve database conflicts"
        exit 1
    else
        print_success "Migrations completed successfully"
    fi
fi

# Setup JavaScript assets
print_status "Setting up JavaScript assets..."
npm run setup

# Reset Elasticsearch indices
print_status "Setting up Elasticsearch indices..."
print_warning "This may take a few minutes..."

# Start Rails console and run the reindex command
bin/rails runner "
begin
  DevTools.delete_all_indices_and_reindex_all
  puts 'Elasticsearch indices reset successfully'
rescue => e
  puts 'Warning: Elasticsearch indexing failed: ' + e.message
  puts 'You can run this manually later: DevTools.delete_all_indices_and_reindex_all'
end
"

print_success "Setup completed successfully!"

echo ""
echo "=================================================="
echo -e "${GREEN}🎉 Gumroad is ready to run!${NC}"
echo "=================================================="
echo ""
echo -e "${BLUE}To start the application:${NC}"
echo "  bin/dev"
echo ""
echo -e "${BLUE}The application will be available at:${NC}"
echo "  https://gumroad.dev"
echo ""
echo -e "${BLUE}Default login credentials:${NC}"
echo "  Email: <EMAIL>"
echo "  Password: password"
echo "  2FA Code: 000000"
echo ""
echo -e "${BLUE}To stop Docker services later:${NC}"
echo "  COMPOSE_PROJECT_NAME=web docker compose -f docker/docker-compose-local.yml down"
echo ""
echo -e "${BLUE}For future development:${NC}"
echo "  # Quick setup (skip system dependencies):"
echo "  ./setup_gumroad.sh --skip-deps"
echo ""
echo "  # Preserve database data:"
echo "  ./setup_gumroad.sh --skip-db-reset"
echo ""
echo "  # Complete reset (when things go wrong):"
echo "  ./setup_gumroad.sh --force-reset"
echo ""
echo "  # Skip Docker setup (if containers already running):"
echo "  ./setup_gumroad.sh --skip-docker"
echo ""

# Ask if user wants to start the application now
read -p "Would you like to start the application now? (y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting Gumroad application..."
    echo ""
    echo -e "${GREEN}Starting Rails server, JavaScript build system, and Sidekiq worker...${NC}"
    echo -e "${YELLOW}Press Ctrl+C to stop the application${NC}"
    echo ""

    # Initialize rbenv and start the application
    if command_exists rbenv; then
        eval "$(rbenv init -)"
    fi

    # Start the application
    bin/dev
fi

# Disable the exit trap since we completed successfully
trap - EXIT
