{"name": "gumroad", "version": "1.0.0", "description": "NPM Packages for Gumroad", "main": "build.js", "license": "UNLICENSED", "directories": {"doc": "doc"}, "engines": {"node": "~20.17.0"}, "packageManager": "npm@10.8.2", "dependencies": {"@anycable/web": "^1.1.0", "@paypal/paypal-js": "^8.0.0", "@rails/activestorage": "^7.1.3", "@stripe/connect-js": "^3.3.21", "@stripe/react-connect-js": "^3.3.20", "@stripe/react-stripe-js": "^2.0.0", "@stripe/stripe-js": "^4.0.0", "@tiptap/core": "2.6.5", "@tiptap/extension-character-count": "2.6.5", "@tiptap/extension-code-block-lowlight": "2.6.5", "@tiptap/extension-link": "2.6.5", "@tiptap/extension-placeholder": "2.6.5", "@tiptap/extension-underline": "2.6.5", "@tiptap/pm": "2.6.5", "@tiptap/react": "2.6.5", "@tiptap/starter-kit": "2.6.5", "abortcontroller-polyfill": "^1.7.5", "autolinker": "^4.0.0", "braintree-web": "^3.88.6", "classnames": "^2.3.2", "clipboard": "^2.0.11", "core-js": "^3.27.1", "date-fns": "^3.0.0", "date-fns-tz": "^3.0.0", "dompurify": "^3.2.6", "fast-average-color": "^9.4.0", "immer": "^10.0.0", "jquery": "^3.6.3", "js-yaml": "^4.1.0", "json-schema-to-ts": "^3.0.0", "libphonenumber-js": "^1.10.15", "lodash": "^4.17.21", "lowlight": "^3.0.0", "mailcheck": "^1.1.1", "pdfjs-dist": "4.5.136", "react": "^18.1.0", "react-calendar": "^5.0.0", "react-dom": "^18.1.0", "react-media-recorder": "^1.7.1", "react-on-rails": "^14.0.0", "react-router-dom": "^6.11.1", "react-select": "^5.7.0", "react-sortablejs": "^6.1.4", "recharts": "^2.12.1", "sortablejs": "^1.15.0", "ts-safe-cast": "^1.0.2", "whatwg-fetch": "^3.6.19"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/js": "^9.6.0", "@types/braintree-web": "^3.75.22", "@types/facebook-js-sdk": "^3.3.6", "@types/facebook-pixel": "^0.0.31", "@types/google.analytics": "^0.0.46", "@types/grecaptcha": "^3.0.4", "@types/gtag.js": "^0.0.20", "@types/jquery": "^3.5.16", "@types/jwplayer": "^8.28.1", "@types/lodash": "^4.14.191", "@types/mailcheck": "^1.1.33", "@types/node": "^20.0.0", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@types/sortablejs": "^1.15.0", "@types/webpack-env": "^1.18.0", "compression-webpack-plugin": "^11.0.0", "css-loader": "^7.0.0", "esbuild-loader": "^4.0.0", "eslint": "^9.6.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.31.11", "fork-ts-checker-webpack-plugin": "^9.0.0", "globals": "^15.8.0", "lint-staged": "^15.0.0", "mini-css-extract-plugin": "^2.7.2", "patch-package": "^8.0.0", "postcss": "^8.4.31", "postcss-loader": "^8.0.0", "postcss-preset-env": "^10.0.0", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "sass": "~1.77.0", "sass-loader": "^16.0.0", "style-loader": "^4.0.0", "svgo": "^3.0.2", "tailwindcss": "^3.4.10", "terser-webpack-plugin": "^5.3.6", "typescript": "~5.5.0", "typescript-eslint": "^8.0.0", "webpack": "^5.75.0", "webpack-assets-manifest": "^5.0.6", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.0.0", "webpack-dev-server": "^5.0.0", "webpack-merge": "^6.0.0"}, "overrides": {"@tiptap/extension-blockquote": "$@tiptap/starter-kit", "@tiptap/extension-bold": "$@tiptap/starter-kit", "@tiptap/extension-bubble-menu": "$@tiptap/react", "@tiptap/extension-bullet-list": "$@tiptap/starter-kit", "@tiptap/extension-code-block": "$@tiptap/starter-kit", "@tiptap/extension-code": "$@tiptap/starter-kit", "@tiptap/extension-document": "$@tiptap/starter-kit", "@tiptap/extension-dropcursor": "$@tiptap/starter-kit", "@tiptap/extension-floating-menu": "$@tiptap/react", "@tiptap/extension-gapcursor": "$@tiptap/starter-kit", "@tiptap/extension-hard-break": "$@tiptap/starter-kit", "@tiptap/extension-history": "$@tiptap/starter-kit", "@tiptap/extension-horizontal-rule": "$@tiptap/starter-kit", "@tiptap/extension-italic": "$@tiptap/starter-kit", "@tiptap/extension-list-item": "$@tiptap/starter-kit", "@tiptap/extension-ordered-list": "$@tiptap/starter-kit", "@tiptap/extension-paragraph": "$@tiptap/starter-kit", "@tiptap/extension-strike": "$@tiptap/starter-kit", "@tiptap/extension-text": "$@tiptap/starter-kit", "webpack": "$webpack", "csstype": "3.1.3"}, "scripts": {"build": "bin/shakapacker --progress --color", "watch": "bin/shakapacker --progress --color --watch", "analyze": "WEBPACK_ANALYZE=1 bin/shakapacker --progress", "lint-fast": "DISABLE_TYPE_CHECKED=1 npx eslint", "setup": "bundle exec rails js:export && node lib/findIcons.js", "postinstall": "patch-package"}, "repository": {"type": "git", "url": "git+https://github.com/antiwork/gumroad.git"}, "type": "module", "author": "Gumroad Developers", "browserslist": ["defaults and supports es6"], "lint-staged": {"*.{js,jsx,ts,tsx,cjs,mjs}": "npm run lint-fast -- --max-warnings 0 --fix --no-warn-ignored", "*.{css,scss,md,json}": "prettier --write", "*.rb": "bundle exec rubocop --force-exclusion -a", "*.svg": "svgo --multipass"}, "postcss": {"plugins": {"tailwindcss": {}}}}