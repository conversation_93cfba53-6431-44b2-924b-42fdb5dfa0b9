GIT
  remote: https://github.com/citizensadvice/capybara_accessible_selectors.git
  revision: 161b8c5b1a0a5408af914d7544956372e9da2c9e
  ref: 161b8c5b1a0a5408af914d7544956372e9da2c9e
  specs:
    capybara_accessible_selectors (0.10.0)
      capybara (~> 3.36)

GIT
  remote: https://github.com/isaacsanders/omniauth-stripe-connect.git
  revision: 468dd9acaccdbba38a38cdbcdf7f10c17be25e89
  ref: 468dd9acaccdbba38a38cdbcdf7f10c17be25e89
  specs:
    omniauth-stripe-connect (2.10.1)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.4)

GEM
  remote: https://gems.contribsys.com/
  specs:
    sidekiq-pro (7.3.3)
      sidekiq (>= 7.3.0, < 8)

GEM
  remote: https://rubygems.org/
  specs:
    Ascii85 (1.1.1)
    acme-client (2.0.18)
      faraday (>= 1.0, < 3.0.0)
      faraday-retry (>= 1.0, < 3.0.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actionpack-action_caching (1.2.2)
      actionpack (>= 4.0.0)
    actionpack-cloudflare (1.1.0)
      actionpack (>= 3.2)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_hash (3.3.1)
      activesupport (>= 5.0.0)
    active_model_otp (2.3.4)
      activemodel
      rotp (~> 6.3.0)
    active_record_query_trace (1.8.3)
      activerecord (>= 6.0.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-mysql-index-hint (0.0.4)
      activerecord (>= 3.1)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aes_key_wrap (1.1.0)
    afm (0.2.2)
    after_commit_everywhere (1.4.0)
      activerecord (>= 4.2)
      activesupport
    alterity (1.4.2)
      mysql2 (>= 0.3)
      rails (>= 6.1)
    ancestry (4.3.3)
      activerecord (>= 5.2.6)
    anycable (1.5.2)
      anycable-core (= 1.5.2)
      grpc (~> 1.6)
    anycable-core (1.5.2)
      anyway_config (~> 2.2)
      base64 (>= 0.2)
      google-protobuf (~> 4)
      stringio (~> 3)
    anycable-rails (1.5.6)
      anycable (~> 1.5.0)
      anycable-rails-core (= 1.5.6)
    anycable-rails-core (1.5.6)
      actioncable (>= 7.0, < 9.0)
      anycable-core (~> 1.5.0)
      globalid
    anyway_config (2.7.0)
      ruby-next-core (~> 1.0)
    apple_id (1.5.2)
      json-jwt (~> 1.15.2)
      openid_connect (~> 1.3.0)
      rack-oauth2 (~> 1.21.2)
    archive-zip (0.12.0)
      io-like (~> 0.3.0)
    ast (2.4.2)
    attr_extras (7.1.0)
    attr_required (1.0.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.963.0)
    aws-sdk-autoscaling (1.114.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.201.4)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-elastictranscoder (1.57.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-mediaconvert (1.134.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.157.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-sns (1.82.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-sqs (1.80.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.9.1)
      aws-eventstream (~> 1, >= 1.0.2)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    benchmark-ips (2.13.0)
    bigdecimal (3.1.8)
    bindata (2.5.0)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    braintree (4.21.0)
      builder (>= 3.2.4)
      rexml (>= 3.1.9)
    bson (5.1.1)
    buftok (0.3.0)
    bugsnag (6.27.1)
      concurrent-ruby (~> 1.0)
    builder (3.3.0)
    byebug (11.1.3)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    closure_tree (7.4.0)
      activerecord (>= 4.2.10)
      with_advisory_lock (>= 4.0.0)
    coderay (1.1.3)
    color (1.8)
    colorize (1.1.0)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    cookiejar (0.3.4)
    countries (6.0.1)
      unaccent (~> 0.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.17.1)
      addressable
    csv (3.3.0)
    dalli (3.2.8)
    database_cleaner-active_record (2.2.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    database_cleaner-mongoid (2.0.1)
      database_cleaner-core (~> 2.0.0)
      mongoid
    date (3.3.4)
    dead_end (4.0.0)
    declarative (0.0.20)
    derailed_benchmarks (2.1.2)
      benchmark-ips (~> 2)
      dead_end
      get_process_mem (~> 0)
      heapy (~> 0)
      memory_profiler (>= 0, < 2)
      mini_histogram (>= 0.3.0)
      rack (>= 1)
      rack-test
      rake (> 10, < 14)
      ruby-statistics (>= 2.1)
      thor (>= 0.19, < 2)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-pwned_password (0.1.12)
      devise (~> 4)
      pwned (~> 2.4)
    diff-lcs (1.5.1)
    discordrb (3.5.0)
      discordrb-webhooks (~> 3.5.0)
      ffi (>= 1.9.24)
      opus-ruby
      rest-client (>= 2.0.0)
      websocket-client-simple (>= 0.3.0)
    discordrb-webhooks (3.5.0)
      rest-client (>= 2.0.0)
    domain_name (0.6.20240107)
    doorkeeper (5.7.1)
      railties (>= 5)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    drb (2.2.1)
    easypost (6.4.1)
    elasticsearch (7.11.2)
      elasticsearch-api (= 7.11.2)
      elasticsearch-transport (= 7.11.2)
    elasticsearch-api (7.11.2)
      multi_json
    elasticsearch-dsl (0.1.10)
    elasticsearch-model (7.1.1)
      activesupport (> 3)
      elasticsearch (> 1)
      hashie
    elasticsearch-rails (7.1.1)
    elasticsearch-transport (7.11.2)
      faraday (~> 1)
      multi_json
    em-http-request (1.1.7)
      addressable (>= 2.3.4)
      cookiejar (!= 0.3.1)
      em-socksify (>= 0.3)
      eventmachine (>= 1.0.3)
      http_parser.rb (>= 0.6.0)
    em-socksify (0.3.3)
      base64
      eventmachine (>= 1.0.0.beta.4)
    em-synchrony (1.0.6)
      eventmachine (>= 1.0.0.beta.1)
    epub-cfi (0.1.3)
      racc
    epub-parser (0.4.8)
      addressable (>= 2.3.5)
      archive-zip
      epub-cfi
      rchardet (>= 1.6.1)
      rexml
    equalizer (0.0.11)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    ethon (0.16.0)
      ffi (>= 1.15.0)
    event_emitter (0.2.6)
    event_stream_parser (1.0.0)
    eventmachine (1.2.7)
    eventmachine_httpserver (0.2.1)
    execjs (2.9.1)
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.4.2)
      i18n (>= 1.8.11, < 2)
    faraday (1.10.3)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.0)
      faraday (~> 1.0)
    ffi (1.17.0)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    flag_shih_tzu (0.3.23)
    flipper (1.3.0)
      concurrent-ruby (< 2)
    flipper-redis (1.3.0)
      flipper (~> 1.3.0)
      redis (>= 3.0, < 6)
    flipper-ui (1.3.0)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 1.3.0)
      rack (>= 1.4, < 4)
      rack-protection (>= 1.5.3, < 5.0.0)
      rack-session (>= 1.0.2, < 3.0.0)
      sanitize (< 7)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    get_process_mem (0.2.7)
      ffi (~> 1.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-androidpublisher_v3 (0.68.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (0.15.1)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    google-protobuf (4.29.3)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.29.3-arm64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.29.3-x86_64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.29.3-x86_64-linux)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos-types (1.18.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.11.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    grpc (1.70.1)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.70.1-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.70.1-x86_64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.70.1-x86_64-linux)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    hashdiff (1.1.1)
    hashery (2.1.2)
    hashie (5.0.0)
    heapy (0.2.0)
      thor
    htmlentities (4.3.4)
    http-2 (0.12.0)
      base64
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-accept (1.7.0)
    http-cookie (1.0.6)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    http_accept_language (2.1.1)
    http_parser.rb (0.6.0)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    ibandit (1.21.0)
      i18n
    image_processing (1.13.0)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    image_sorcery (1.1.0)
    io-console (0.7.2)
    io-like (0.3.1)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.12.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    js-routes (2.2.8)
      railties (>= 4)
    json (2.7.2)
    json-jwt (********)
      activesupport (>= 4.2)
      aes_key_wrap
      bindata
      httpclient
    json-schema (5.0.0)
      addressable (~> 2.8)
    json_matchers (0.11.1)
      json_schema
    json_schema (0.21.0)
    jwt (2.8.2)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    knapsack_pro (7.8.0)
      rake
    koala (3.6.0)
      addressable
      base64
      faraday
      faraday-multipart
      json (>= 1.8)
      rexml
    language_server-protocol (********)
    libv8-node (*********)
    libv8-node (*********-arm64-darwin)
    libv8-node (*********-x86_64-darwin)
    libv8-node (*********-x86_64-linux)
    libv8-node (*********-x86_64-linux-musl)
    llhttp-ffi (0.5.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.6.0)
    lograge (0.14.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    makara (0.6.0.pre)
      activerecord (>= 5.2.0)
    marcel (1.0.4)
    matrix (0.4.2)
    maxmind-db (1.2.0)
    maxmind-geoip2 (1.2.0)
      connection_pool (~> 2.2)
      http (>= 4.3, < 6.0)
      maxmind-db (~> 1.2)
    memoizable (0.4.2)
      thread_safe (~> 0.3, >= 0.3.1)
    memory_profiler (1.0.2)
    method_source (1.1.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.0806)
    mini_histogram (0.3.1)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    mini_racer (0.16.0)
      libv8-node (~> *********)
    minitest (5.25.1)
    model_attribute (3.2.0)
    modis (4.3.1)
      activemodel (>= 5.2)
      activesupport (>= 5.2)
      connection_pool (>= 2)
      msgpack (>= 0.5)
      redis (>= 3.0)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    mongo (2.20.1)
      bson (>= 4.14.1, < 6.0.0)
    mongoid (9.0.1)
      activemodel (>= 5.1, < 7.2, != 7.0.0)
      concurrent-ruby (>= 1.0.5, < 2.0)
      mongo (>= 2.18.0, < 3.0.0)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.2.0)
    mysql2 (0.5.6)
    naught (1.1.0)
    net-http-persistent (4.0.4)
      connection_pool (~> 2.2)
    net-http2 (0.18.5)
      http-2 (~> 0.11)
    net-imap (0.4.14)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.3)
    nokogiri (1.16.7)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    oauth (1.1.0)
      oauth-tty (~> 1.0, >= 1.0.1)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oauth-tty (1.0.5)
      version_gem (~> 1.1, >= 1.1.1)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-facebook (10.0.0)
      bigdecimal
      omniauth-oauth2 (>= 1.2, < 3)
    omniauth-google-oauth2 (1.1.2)
      jwt (>= 2.0)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth (1.2.0)
      oauth
      omniauth (>= 1.0, < 3)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    omniauth-twitter (1.4.0)
      omniauth-oauth (~> 1.1)
      rack
    openid_connect (1.3.1)
      activemodel
      attr_required (>= 1.0.0)
      json-jwt (>= 1.5.0)
      net-smtp
      rack-oauth2 (>= 1.6.1)
      swd (>= 1.0.0)
      tzinfo
      validate_email
      validate_url
      webfinger (>= 1.0.1)
    openssl (3.2.0)
    optimist (3.1.0)
    opus-ruby (1.0.1)
      ffi
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    package_json (0.1.0)
    pagy (9.0.5)
    paper_trail (15.1.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.26.3)
    parser (3.3.5.0)
      ast (~> 2.4.1)
      racc
    patience_diff (1.2.0)
      optimist (~> 3.0)
    paypal-checkout-sdk (1.0.5)
      paypalhttp (~> 1.0.1)
    paypal-sdk-core (0.3.5)
      multi_json (~> 1.0)
      xml-simple
    paypal-sdk-merchant (1.117.3)
      paypal-sdk-core (~> 0.3.0)
    paypalhttp (1.0.1)
    pdf-core (0.10.0)
    pdf-reader (2.12.0)
      Ascii85 (~> 1.0)
      afm (~> 0.2.1)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pdfkit (*******)
    phony (2.20.12)
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    premailer (1.23.0)
      addressable
      css_parser (>= 1.12.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    private_address_check (0.5.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (5.2.3)
      date
      stringio
    public_suffix (5.1.1)
    puffing-billy (4.0.0)
      addressable (~> 2.5)
      em-http-request (~> 1.1, >= 1.1.0)
      em-synchrony
      eventmachine (~> 1.2)
      eventmachine_httpserver
      http_parser.rb (~> 0.6.0)
      multi_json
    puma (6.4.2)
      nio4r (~> 2.0)
    pundit (2.3.2)
      activesupport (>= 3.0.0)
    pwned (2.4.1)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.7)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-mini-profiler (4.0.0)
      rack (>= 1.2.0)
    rack-oauth2 (1.21.3)
      activesupport
      attr_required
      httpclient
      json-jwt (>= 1.11.0)
      rack (>= 2.1.0)
    rack-protection (4.0.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0, < 4)
    rack-proxy (0.7.7)
      rack
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-ssl (1.4.1)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rack-timeout (0.7.0)
    rack-utf8_sanitizer (1.9.1)
      rack (>= 1.0, < 4.0)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    ratelimit (1.1.0)
      redis (>= 3.0.0)
      redis-namespace (>= 1.0.0)
    rchardet (1.8.0)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    react_on_rails (14.0.4)
      addressable
      connection_pool
      execjs (~> 2.5)
      rails (>= 5.2)
      rainbow (~> 3.0)
    redcarpet (3.6.0)
    redis (5.2.0)
      redis-client (>= 0.22.0)
    redis-client (0.22.2)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    regexp_parser (2.9.2)
    reline (0.5.9)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    resend (0.16.0)
      httparty (>= 0.21.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.3.7)
    rinku (2.0.6)
    rotp (6.3.0)
    rouge (4.3.0)
    rpush (9.2.0)
      activesupport (>= 6.0, != 7.2.1, != 7.2.0, != 7.1.4)
      googleauth
      jwt (>= 1.5.6)
      multi_json (~> 1.0)
      net-http-persistent
      net-http2 (~> 0.18, >= 0.18.3)
      railties
      rainbow
      thor (>= 0.18.1, < 2.0)
      web-push
    rpush-redis (1.2.0)
      modis (>= 3.0, < 5.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-github (2.4.0)
      rspec-core (~> 3.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.3)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-retry (0.6.2)
      rspec-core (> 3.3)
    rspec-sidekiq (5.0.0)
      rspec-core (~> 3.0)
      rspec-expectations (~> 3.0)
      rspec-mocks (~> 3.0)
      sidekiq (>= 5, < 8)
    rspec-support (3.13.1)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.65.1)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.4, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.32.3)
      parser (>= 3.3.1.0)
    rubocop-performance (1.21.1)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.26.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rake (0.6.0)
      rubocop (~> 1.0)
    rubocop-rspec (3.0.4)
      rubocop (~> 1.61)
    ruby-limiter (2.3.0)
    ruby-next-core (1.1.1)
    ruby-oembed (0.17.0)
    ruby-openai (7.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    ruby-statistics (4.0.0)
    ruby-vips (2.2.2)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    ruby_http_client (3.5.5)
    rubyzip (2.3.2)
    sanitize (6.1.2)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    secure_headers (6.7.0)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semantic_range (3.0.0)
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    shakapacker (8.0.1)
      activesupport (>= 5.2)
      package_json
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    shoulda-matchers (6.3.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.0)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-cron (1.12.0)
      fugit (~> 1.8)
      globalid (>= 1.0.1)
      sidekiq (>= 6)
    sidekiq-unique-jobs (8.0.10)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      sidekiq (>= 7.0.0, < 8.0.0)
      thor (>= 1.0, < 3.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_oauth (0.3.1)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    slack-notifier (2.4.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    spring (4.2.1)
    spring-commands-rspec (1.0.4)
      spring (>= 0.9.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    ssrf_filter (1.2.0)
    stackprof (0.2.26)
    state_machines (0.6.0)
    state_machines-activemodel (0.9.0)
      activemodel (>= 6.0)
      state_machines (>= 0.6.0)
    state_machines-activerecord (0.9.0)
      activerecord (>= 6.0)
      state_machines-activemodel (>= 0.9.0)
    streamio-ffmpeg (3.0.2)
      multi_json (~> 1.8)
    stringio (3.1.1)
    stripe (12.5.0)
    strongbox (0.7.3)
      activerecord
    subexec (0.2.3)
    suo (0.4.0)
      dalli
      msgpack
      redis
    super_diff (0.12.1)
      attr_extras (>= 6.2.4)
      diff-lcs
      patience_diff
    swd (1.3.0)
      activesupport (>= 3)
      attr_required (>= 0.0.5)
      httpclient (>= 2.4)
    taxjar-ruby (3.0.4)
      addressable (~> 2.3)
      http (>= 4.3)
      memoizable (~> 0.4.0)
      model_attribute (~> 3.2)
    terser (1.2.3)
      execjs (>= 0.3.0, < 3)
    thor (1.3.1)
    thread_safe (0.3.6)
    tilt (2.4.0)
    timeout (0.4.1)
    trailblazer-option (0.1.2)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    twitter (8.1.0)
      addressable (~> 2.8)
      buftok (~> 0.3.0)
      equalizer (~> 0.0.11)
      http (~> 5.2)
      http-form_data (~> 2.3)
      llhttp-ffi (~> 0.5.0)
      memoizable (~> 0.4.0)
      multipart-post (~> 2.4)
      naught (~> 1.1)
      simple_oauth (~> 0.3.0)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unaccent (0.4.0)
    unicode-display_width (2.5.0)
    validate_email (0.1.6)
      activemodel (>= 3.0)
      mail (>= 2.2.5)
    validate_url (1.0.15)
      activemodel (>= 3.0.0)
      public_suffix
    valvat (1.4.4)
      rexml (>= 3.2.7, < 4.0.0)
    vcr (6.2.0)
    version_gem (1.1.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-push (3.0.1)
      jwt (~> 2.0)
      openssl (~> 3.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webfinger (2.0.0)
      activesupport
      faraday (~> 1.7)
      faraday_middleware (~> 1.1)
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.8.1)
    websocket (1.2.11)
    websocket-client-simple (0.8.0)
      event_emitter
      websocket
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    with_advisory_lock (5.1.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    xml-simple (1.1.9)
      rexml
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.17)
    zip-zip (0.3)
      rubyzip (>= 1.0.0)

PLATFORMS
  arm64-darwin
  ruby
  x86_64-darwin
  x86_64-linux
  x86_64-linux-musl

DEPENDENCIES
  acme-client (~> 2.0)
  actionpack-action_caching (~> 1.2)
  actionpack-cloudflare (~> 1.1)
  active_hash (~> 3.3)
  active_model_otp (~> 2.3)
  active_record_query_trace (~> 1.8)
  activerecord-mysql-index-hint (~> 0.0)
  after_commit_everywhere (~> 1.3)
  alterity (~> 1.4)
  ancestry (~> 4.2)
  anycable-rails (~> 1.5)
  apple_id (~> 1.5)
  aws-sdk-autoscaling (~> 1.84)
  aws-sdk-elastictranscoder (~> 1.39)
  aws-sdk-mediaconvert (~> 1.96)
  aws-sdk-s3 (~> 1.117)
  aws-sdk-sns (~> 1.57)
  aws-sdk-sqs (~> 1.69)
  babel-transpiler (~> 0.7)
  benchmark (~> 0.4)
  bootsnap (~> 1.15)
  braintree (~> 4.9)
  bugsnag (~> 6.25)
  builder (~> 3.2)
  bundler
  byebug (~> 11.1)
  capybara (~> 3.38)
  capybara_accessible_selectors!
  closure_tree (~> 7.4)
  color (~> 1.8)
  colorize (~> 1.0)
  countries (~> 6.0)
  csv (~> 3.3)
  database_cleaner-active_record (~> 2.0)
  database_cleaner-mongoid (~> 2.0)
  derailed_benchmarks (~> 2.1)
  devise (~> 4.8)
  devise-pwned_password (~> 0.1)
  discordrb (~> 3.4)
  doorkeeper (= 5.7.1)
  dotenv (~> 2.8)
  dotenv-rails (~> 2.8)
  easypost (~> 6.0)
  elasticsearch (= 7.11.2)
  elasticsearch-api (= 7.11.2)
  elasticsearch-dsl (= 0.1.10)
  elasticsearch-model (= 7.1.1)
  elasticsearch-rails (= 7.1.1)
  elasticsearch-transport (= 7.11.2)
  epub-parser (~> 0.4)
  factory_bot_rails (~> 6.4)
  faker (~> 3.1)
  flag_shih_tzu (~> 0.3)
  flipper (~> 1.3.0)
  flipper-redis (~> 1.3.0)
  flipper-ui (~> 1.3.0)
  friendly_id (~> 5.5)
  google-apis-androidpublisher_v3 (~> 0.64)
  googleauth (~> 1.7)
  htmlentities (~> 4.3)
  http_accept_language (~> 2.1)
  httparty (~> 0.21)
  ibandit (~> 1.12)
  image_processing (~> 1.12)
  image_sorcery (~> 1.1)
  jbuilder (~> 2.11)
  js-routes (~> 2.2)
  json (~> 2.6)
  json-schema (~> 5.0)
  json_matchers (~> 0.11)
  kaminari (~> 1.2)
  knapsack_pro (~> 7.0)
  koala (~> 3.3)
  lograge (~> 0.12)
  makara (= 0.6.0.pre)
  maxmind-geoip2 (~> 1.1)
  mime-types (~> 3.4)
  mini_magick (~> 4.12)
  mini_racer (= 0.16.0)
  money (~> 6.16)
  mongoid (~> 9.0)
  mysql2 (>= 0.5.6)
  nokogiri (~> 1.13)
  omniauth-facebook (~> 10.0)
  omniauth-google-oauth2 (~> 1.1, >= 1.1.1)
  omniauth-rails_csrf_protection (~> 1.0)
  omniauth-stripe-connect!
  omniauth-twitter (~> 1.4)
  ostruct (~> 0.6)
  pagy (~> 9.0.0)
  paper_trail (~> 15.0)
  paypal-checkout-sdk (~> 1.0)
  paypal-sdk-merchant (~> 1.117)
  pdf-reader (~> 2.11)
  pdfkit (~> 0.8)
  phony (~> 2.20)
  prawn (~> 2.4)
  premailer-rails (~> 1.12)
  private_address_check (~> 0.5)
  pry-byebug (~> 3.10)
  pry-rails (~> 0.3)
  psych (~> 5.2.3)
  public_suffix (~> 5.0)
  puffing-billy (~> 4.0.0)
  puma (= 6.4.2)
  pundit (~> 2.3)
  rack-attack (~> 6.6)
  rack-cors (~> 2.0)
  rack-mini-profiler (~> 4.0)
  rack-ssl (~> 1.4)
  rack-timeout (~> 0.6)
  rack-utf8_sanitizer (~> 1.8)
  rails (= *******)
  rails-controller-testing (~> 1.0)
  rake (= 13.2.1)
  ratelimit (~> 1.0)
  react_on_rails (~> 14.0)
  redcarpet (~> 3.5)
  redis (~> 5.0)
  redis-namespace (~> 1.10)
  resend (~> 0.16)
  rinku (~> 2.0)
  rouge (~> 4.0)
  rpush (~> 9.1)
  rpush-redis (~> 1.2)
  rspec (~> 3.12)
  rspec-github (~> 2.4.0)
  rspec-rails (~> 6.0)
  rspec-retry (~> 0.6)
  rspec-sidekiq (~> 5.0)
  rspec_junit_formatter (~> 0.6)
  rubocop (~> 1.65.0)
  rubocop-performance (~> 1.21.0)
  rubocop-rails (~> 2.26.0)
  rubocop-rake (~> 0.6.0)
  rubocop-rspec (~> 3.0.0)
  ruby-limiter (~> 2.2)
  ruby-oembed (~> 0.16)
  ruby-openai (~> 7.0)
  rubyzip (~> 2.3)
  sass-rails (~> 6.0)
  secure_headers (~> 6.5)
  selenium-webdriver (~> 4.7)
  sendgrid-ruby (~> 6.6)
  shakapacker (~> 8.0)
  shoulda-matchers (~> 6.0)
  sidekiq (~> 7.2)
  sidekiq-cron (~> 1.9)
  sidekiq-pro (~> 7.2)!
  sidekiq-unique-jobs (~> 8.0)
  sitemap_generator (~> 6.3)
  slack-notifier (~> 2.4)
  spring (~> 4.0)
  spring-commands-rspec (~> 1.0)
  sprockets-rails (~> 3.4)
  ssrf_filter (~> 1.2.0)
  stackprof (~> 0.2)
  state_machines-activerecord (~> 0.8)
  streamio-ffmpeg (~> 3.0)
  stripe (~> 12.0)
  strongbox (~> 0.7)
  subexec (~> 0.2)
  suo (~> 0.4)
  super_diff (~> 0.12.0)
  taxjar-ruby (~> 3.0)
  terser (~> 1.1)
  twitter (~> 8.0)
  typhoeus (~> 1.4)
  valvat (~> 1.2)
  vcr (~> 6.1)
  warden (~> 1.2)
  webdrivers (~> 5.2)
  webmock (~> 3.18)
  zip-zip (~> 0.3)

RUBY VERSION
   ruby 3.4.3p32

BUNDLED WITH
   2.5.10
