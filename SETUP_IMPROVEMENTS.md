# Gumroad Setup Script Improvements

## Overview

The `setup_gumroad.sh` script has been significantly enhanced to address database migration issues and provide a more robust development workflow. The script now supports multiple execution modes and provides better error handling and recovery options.

## Key Improvements

### 1. Command Line Options

The script now supports several command-line options for different scenarios:

- `--force-reset`: Complete reset of everything (database, containers, etc.)
- `--skip-deps`: Skip system dependency installation (for quick re-runs)
- `--skip-docker`: Skip Docker container setup (if already running)
- `--skip-db-reset`: Skip database reset (preserves existing data)
- `--verbose`: Enable verbose output for debugging
- `--help`: Show usage information

### 2. Database Migration Handling

**Problem Solved**: The original script used `bin/rails db:prepare` which could fail when adding new tables or when migrations were in an inconsistent state.

**Solution**: 
- Added a robust `reset_database()` function that completely drops and recreates databases
- Implements graceful fallback: tries gentle setup first, falls back to reset if needed
- Provides options to preserve data or force complete reset
- Better error messages and recovery suggestions

### 3. Docker Service Management

**Improvements**:
- Added proper health checks using `wait_for_service()` function
- Checks for service availability on specific ports using `netcat`
- Better container lifecycle management (stop, cleanup, start)
- Support for skipping Docker setup if services are already running
- Volume cleanup on force reset

### 4. Development Workflow Support

**For Daily Development**:
```bash
# Quick setup when you've made code changes
./setup_gumroad.sh --skip-deps

# Preserve your database data
./setup_gumroad.sh --skip-db-reset

# When things go wrong (complete reset)
./setup_gumroad.sh --force-reset
```

### 5. Error Handling and Recovery

- Added exit trap for cleanup on failure
- Better error messages with suggested recovery actions
- Verbose mode for debugging issues
- Graceful handling of missing dependencies

### 6. Smart Dependency Management

- Wraps system dependency installation in conditional blocks
- Allows skipping time-consuming installations on re-runs
- Still validates critical dependencies (Docker, Ruby, Node.js)
- Preserves existing configurations when possible

## Usage Examples

### Initial Setup (First Time)
```bash
./setup_gumroad.sh
```

### Quick Re-run After Code Changes
```bash
./setup_gumroad.sh --skip-deps
```

### When Database Migrations Fail
```bash
./setup_gumroad.sh --force-reset
```

### Preserve Database During Updates
```bash
./setup_gumroad.sh --skip-db-reset
```

### Debug Issues
```bash
./setup_gumroad.sh --verbose
```

## Benefits for Development Workflow

1. **Faster Iterations**: Skip time-consuming dependency installations
2. **Data Preservation**: Option to keep database data during updates
3. **Reliable Recovery**: Force reset option when things go wrong
4. **Better Debugging**: Verbose mode and improved error messages
5. **Flexible Execution**: Mix and match options for different scenarios

## Database Migration Strategy

The script now handles database migrations in a more robust way:

1. **Gentle Approach**: First tries `bin/rails db:prepare`
2. **Fallback**: If that fails, performs complete database reset
3. **Force Reset**: Option to skip gentle approach and reset immediately
4. **Preserve Data**: Option to skip database reset entirely

This approach ensures that:
- New tables and migrations are handled properly
- Existing data can be preserved when desired
- Complete recovery is possible when migrations are broken
- Development workflow is not blocked by database issues

## Future Maintenance

The script is now more maintainable and extensible:
- Modular functions for different operations
- Clear separation of concerns
- Consistent error handling patterns
- Easy to add new options or modify behavior

This enhanced setup script should resolve your database migration issues and provide a much smoother development experience.
