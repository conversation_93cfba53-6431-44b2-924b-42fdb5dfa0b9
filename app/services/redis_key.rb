# frozen_string_literal: true

class RedisKey
  class << self
    def total_made = "homepage:total_made"
    def number_of_creators = "company_page:number_of_creators"
    def prev_week_payout_usd = "homepage:prev_week_payout_usd"
    def balance_stats_sales_caching_threshold = "balance_stats:sales_caching_threshold"
    def balance_stats_users_excluded_from_caching = "balance_stats:users_excluded_from_caching"
    def balance_stats_scheduler_minutes_between_jobs = "balance_stats_scheduler:minutes_between_jobs"
    def elasticsearch_indexer_worker_ignore_404_errors_on_indices = "elasticsearch_indexer_worker:ignore_404_errors_on_indices"
    def product_presenter_existing_product_files_limit = "product_presenter:existing_product_files_limit"
    def seller_analytics_cache_version = "seller_analytics:cache_version"
    def cf_cache_invalidated_extensions_and_cache_keys = "cf_cache_invalidated_extensions_and_cache_keys"
    def user_ids_with_payment_requirements_key = "user_ids_with_payment_requirements_key"
    def card_testing_product_watch_minutes = "card_testing_product_watch_minutes"
    def card_testing_product_max_failed_purchases_count = "card_testing_product_max_failed_purchases_count"
    def card_testing_product_block_hours = "card_testing_product_block_hours"
    def card_testing_max_number_of_failed_purchases_in_a_row = "card_testing_max_number_of_failed_purchases_in_a_row"
    def card_testing_failed_purchases_in_a_row_watch_days = "card_testing_failed_purchases_in_a_row_watch_days"
    def followers_import_limit = "followers_import:limit"
    def force_product_id_timestamp = "force_product_id_timestamp"
    def api_v2_sales_deprecated_pagination_query_timeout = "api_v2_sales_deprecated_pagination_query_timeout"
    def free_purchases_watch_hours = "free_purchases_watch_hours"
    def max_allowed_free_purchases_of_same_product = "max_allowed_free_purchases_of_same_product"
    def fraudulent_free_purchases_block_hours = "fraudulent_free_purchases_block_hours"
    def sales_related_products_internal_limit = "sales_related_products_internal_limit"
    def recommended_products_associated_product_ids_limit = "recommended_products_associated_product_ids_limit"
    def blast_recipients_slice_size = "blast:recipients_slice_size"
    def impersonated_user(admin_user_id) = "impersonated_user_by_admin_#{admin_user_id}"
    def gumroad_day_date = "gumroad_day_date"
    def update_cached_srpis_job_delay_hours = "update_cached_srpis_job_delay_hours"
    def iffy_moderation_probability = "iffy_moderation_probability"
    def tip_options = "tip_options"
    def default_tip_option = "default_tip_option"
    def create_canada_monthly_sales_report_job_max_execution_time_seconds = "create_canada_monthly_sales_report_job:max_execution_time_seconds"
    def generate_quarterly_sales_report_job_max_execution_time_seconds = "generate_quarterly_sales_report_job:max_execution_time_seconds"
    def generate_canada_sales_report_job_max_execution_time_seconds = "generate_canada_sales_report_job:max_execution_time_seconds"
    def create_vat_report_job_max_execution_time_seconds = "create_vat_report_job:max_execution_time_seconds"
    def transcoded_videos_recentness_limit_in_months = "transcoded_videos_recentness_limit_in_months"
    def generate_fees_by_creator_location_job_max_execution_time_seconds = "generate_fees_by_creator_location_job:max_execution_time_seconds"
    def ytd_sales_report_emails = "reports:ytd_sales_report_emails"
    def failed_seller_purchases_watch_minutes = "failed_seller_purchases_watch_minutes"
    def max_seller_failed_purchases_price_cents = "max_seller_failed_purchases_price_cents"
    def seller_age_threshold_days = "seller_age_threshold_days"
  end
end
