body {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;

  &.iframe {
    background: transparent;
    overflow: hidden;
  }

  & > header,
  header.sticky-top {
    border-bottom: $border;
    grid-column: 1 / span 2;
    grid-row: -3;
  }

  & > footer {
    padding: spacer(4);
    text-align: center;
  }
}

header.hero {
  position: relative;
  z-index: z-index(modal); // So dropdowns correctly overlap the CTA bar
  background-color: var(--body-bg);

  .hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: spacer(1) spacer(4);
    align-items: center;

    .separator {
      width: 100%;
      height: spacer(4);
    }

    .logo-full {
      @include font-size(4);
      margin-right: auto;
    }

    @include breakpoint-up(sm) {
      flex-wrap: nowrap;

      .separator {
        display: none;
      }

      .link-button,
      a:has(.user-avatar) {
        order: 1;
      }
    }

    @include breakpoint-up(lg) {
      flex-wrap: wrap;

      .separator {
        order: 1;
        display: block;
      }
    }
  }
}

body > header,
header.sticky-top,
main > * {
  padding: $body-padding-mobile;

  @include breakpoint-up(lg) {
    padding: $body-padding-desktop $body-padding-desktop-center;

    &:where(.sidebar-nav *) {
      padding-left: $body-padding-desktop;
      padding-right: max(100% - #{$body-max-width} - #{$body-padding-desktop}, #{$body-padding-desktop});
    }
  }
}

body > header,
header.sticky-top,
main > header {
  $gap-x: spacer(3);
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: spacer(4) $gap-x;
  padding-top: $header-padding-y;
  padding-bottom: $header-padding-y;

  & > * {
    grid-column: 1/-1;
  }

  h1 {
    grid-column-end: unset;

    &:where(.sidebar-nav *) {
      display: none;

      @include breakpoint-up(lg) {
        margin: spacer(4) 0;
        @include text-singleline;
        display: initial;
      }
    }
  }

  .actions {
    display: grid;
    grid-template-columns: repeat(
      auto-fit,
      minmax(min(50% - #{$gap-x}, (#{$grid-wrap-breakpoint} - 100%) * 1000), 1fr)
    );
    gap: $gap-x;
  }

  [role="tablist"] {
    @extend .tab-pills;
    overflow-x: auto;
    margin-left: -(spacer(1));
    @include font-size($form-element-font-size);

    &:first-child {
      margin-top: spacer(4);
    }
  }

  @include breakpoint-up(lg) {
    .actions {
      grid-column: 2;
      grid-template-columns: unset;
      grid-auto-flow: column;
    }
  }
}

main {
  overflow: auto;
  grid-column: 1;
  grid-row: 1;

  & > footer {
    text-align: center;
  }

  *:where(& > :not(:first-child)) {
    border-top: $border;
  }

  & > a {
    @extend %big-link;
    padding-top: spacer(7);
    padding-bottom: spacer(7);
  }

  @media print {
    overflow: initial;
  }
}
