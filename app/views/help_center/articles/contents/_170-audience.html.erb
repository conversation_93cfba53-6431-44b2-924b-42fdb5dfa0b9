<% @description = "In this article: Your subscribe page Embed the subscribe form on your website Sending email to your subscribers Using Workflows to keep subscribers engaged Mana" %>
<div class="scoped-tailwind-preflight help-center-article">
  <div>
    <p>In this article:</p>
    <ul>
      <li><a href="#SubscribePage" target="_self">Your subscribe page</a></li>
      <li><a href="#embed-form" target="_self">Embed the subscribe form on your website</a></li>
      <li><a href="#SendPosts" target="_self">Sending email to your subscribers</a></li>
      <li><a href="#Workflows" target="_self">Using Workflows to keep subscribers engaged</a></li>
      <li><a href="#Manage" target="_self">Managing your subscribers</a>
        <ul>
          <li><a href="#Exporting-FmWIn" target="_self">Exporting</a></li>
          <li><a href="#Importing-YQKl5" target="_self">Importing</a></li>
          <li><a href="#Viewing-and-Deleting-Yd5j6" target="_self">Viewing and Deleting</a></li>
        </ul>
      </li>
    </ul>
    <div class="callout-red">
      <p><b>⚠️ Note: </b>Until you've earned $100 after fees, you will only be allowed to send 100 emails at a time. This limitation is in place to prevent spam and protect Gumroad's email reputation. </p>
    </div>
    <p>A subscriber on Gumroad is someone you can send email notifications or updates to via <a href="169-how-to-send-an-update.html">Emails</a> or <a href="131-using-workflows-to-send-automated-updates.html">Workflows</a>. Subscribers may or may not be your customers yet.</p>
    <p>To see a list of your subscribers, simply go to <a href="https://app.gumroad.com/followers">this page</a> from where you can export a CSV of your followers or remove an email from your list.</p>
    <p>To see how your subscribers have increased over a period of time, you can go to your <a href="https://app.gumroad.com/dashboard/audience">Following Analytics dashboard</a>:</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/624583ffab585b230a8a6fa6/file-OXSh5dPLgy.png" %></figure>
    <h3 id="SubscribePage">Your subscribe page</h3>
    <p>You can grow your following without creating a product, and your followers don't need to create Gumroad accounts to learn about what you're up to. Put simply, the subscribe page is like your newsletter's landing page.</p>
    <p>The URL for your subscribe page will be  {username}.gumroad.com/subscribe. You can find the link for your subscribe page in the top right-hand corner of the Subscribe tab on the <a href="https://app.gumroad.com/followers">Audience page</a>. </p>
    <p>Alternatively, you can copy the link to your Subscribe page from the widgets page.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/628256898c9b585083489fd1/file-sF947tdcq7.gif" %></figure>
    <h3 id="embed-form">Embed the subscribe form on your website</h3>
    <p>Go to your <a href="https://app.gumroad.com/widgets">widgets page</a> and scroll down to the "Subscribe form" section. Click “Copy embed code” and paste it in your website’s HTML editor. If you need help finding the proper location to enter the code into your specific site, please refer to your site builder's help documentation.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/628256998c9b585083489fd2/file-g48XVBZuwX.png" %></figure>
    <h3 id="SendPosts">Sending posts to your subscribers</h3>
    <p>To send out an email to your followers, you can go to your <a href="https://www.gumroad.com/posts">Emails</a> dashboard and click <a href="169-how-to-send-an-update.html">New Email</a> in the top right-hand corner.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/655f4837ff620625c3f7efc9/file-ZzT0b4lAV6.png" %></figure>
    <p>Under Audience, you can choose to send this email to your Subscribers only, to your Customers only, Affiliates only, or to Everyone. You can also choose several other parameters to narrow down your target audience even further. <a href="169-how-to-send-an-update.html">Learn more about emails</a>.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/655f4e2ad8c4315d1df511d1/file-SR4bakvUvR.gif" %></figure>
    <p>Write your message, upload as many files as you like, and choose to publish now or schedule for a later date. </p>
    <h3 id="Workflows">Using Workflows to keep Subscribers engaged</h3>
    <p>Make sure your new subscribers receive an email from you as soon as they sign up by creating a <a href="131-using-workflows-to-send-automated-updates.html">Workflow</a>. Workflows are a series of automated emails that are sent to your subscribers relative to the time they sign up.</p>
    <p>Go to your <a href="https://app.gumroad.com/workflows">workflows dashboard</a> and click the pink New workflow button to start a new workflow. Give it a name (only you will be able to see this), then select your Audience (in this example, Subscribers  only).</p>
    <p>Click Add, then Add email to add your first message. If you want your subscribers to receive the message immediately after they follow you, set the timing of the first email to 0 hours after follow.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/655f485961a11f56e8753917/file-c5GRqDvh07.png" %></figure>
    <p>If you want only new subscribers who sign up after you publish this workflow to get this email, click the Only send to new followers toggle under the Attach files button. If this toggle is switched off, the workflow will be sent to all existing followers as well (as long as they satisfy the other filter conditions).</p>
    <p>You can test out what timing, subject lines, or offerings work best with workflow email analytics. On your Workflows tab, you'll be able to see your open and click-through rates as well. <a href="131-using-workflows-to-send-automated-updates.html">Learn more about workflows</a>.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/61e7da5468cd260cc2d32241/file-jVeyJPFDMc.png" %></figure>
    <h3 id="Manage">Managing your subscribers</h3>
    <p>On the <a href="https://gumroad.com/dashboard/audience">Subscribers dashboard</a>, you can export a CSV of your existing subscribers. Simply click the Export icon next to the dropdown date range menu.</p>
    <h3 id="Exporting-FmWIn">Exporting</h3>
    <p>Click the Export button to export a CSV of your subscribers, from a specified time range that you have selected. The CSV will contain the followers' email addresses and the date that they began following you. </p>
    <p>If someone chooses to unsubscribe, they will be automatically removed from your list. </p>
    <h3 id="Importing-YQKl5">Importing</h3>
    <p>Unfortunately, we don’t support the importing of customers or subscribers.</p>
    <p>You can ask your existing audience to sign up on your Gumroad Subscribe page. Alternatively, you can create a <a href="128-creating-offer-codes.html">100% off discount code</a> to give free access to a product. Those who purchase the product will appear on your <a href="268-customer-dashboard.html">Sales page</a> and can then receive <a href="169-how-to-send-an-update.html">emails</a> from you.</p>
    <h3 id="Viewing-and-Deleting-Yd5j6">Viewing and Deleting</h3>
    <p>You can view the email IDs of all your subscribers and the date that they subscribed to you on the <a href="https://app.gumroad.com/followers">Subscribers page</a>. </p>
    <p>If you wish to delete a subscriber from your list, simply click on their email and then delete them from the right-vertical menu that pops-up.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/64fe7e4af15ed05267de56b3/file-tsOmY4Z5D1.png" %></figure>
  </div>
  <div>
    <h3>Related Articles</h3>
    <ul>
      <li><a href="131-using-workflows-to-send-automated-updates.html"><span>Send automated emails with Workflows</span></a></li>
      <li><a href="169-how-to-send-an-update.html"><span>Send email updates</span></a></li>
      <li><a href="54-not-receiving-updates.html"><span>Customers not receiving emails</span></a></li>
    </ul>
  </div>
</div>
