<% @description = "In this article: Terms to know Diving into the data Referrers Locations Exporting a customer sales CSV How to read your CSV Once you've started accruing sales, " %>
<div class="scoped-tailwind-preflight help-center-article">
  <div>
    <p><b>In this article:</b></p>
    <ul>
      <li><a href="#Terms-to-know-UxK3S">Terms to know</a></li>
      <li><a href="#Diving-into-the-data-E-Y2C">Diving into the data</a></li>
      <li><a href="#Referrers-WBsBP">Referrers</a></li>
      <li><a href="#Locations-PoKPf">Locations</a></li>
      <li><a href="#sales-csv">Exporting a customer sales CSV</a></li>
      <li><a href="#How-to-read-your-CSV-vLST9" target="_self">How to read your CSV</a></li>
      <li><a href="#utm-link-tracking">UTM Link Tracking</a></li>
    </ul>
    <hr role="separator">
    <p>Once you've made sales, use analytics to understand who your customers are and how they found you to help you reach out in the right places. We've designed our analytics platform to make it easy to understand and act on the data that matters.</p>
    <h3 id="Terms-to-know-UxK3S">Terms to know</h3>
    <h3>Conversion rate</h3>
    <p>A "conversion" happens when someone views your product and decides to buy it.</p>
    <p>Your conversion rate is the percentage of viewers who become buyers—for example, if 10 people view your product and 5 purchase, your conversion rate is 50%.</p>
    <h3>Direct sales</h3>
    <p>This will show up in your Analytics page as Direct, Email, IM, but this is, unfortunately, not the entire story. Here's a direct guide to the things that can mean "Direct":</p>
    <ul>
      <li>Mobile devices or apps</li>
      <li>Email</li>
      <li>Twitter clients (Tweetdeck, Hootsuite, bit.ly, etc.)</li>
      <li>Instant messengers (Skype, Gchat, etc.)</li>
    </ul>
    <h3 id="Diving-into-the-data-E-Y2C">Diving into the data</h3>
    <h3>Selecting products</h3>
    <p>By default, data for all of your products is shown in the <a href="https://app.gumroad.com/dashboard/sales">Sales Analytics Dashboard</a>. This allows you to get an overview of all your Gumroad products.</p>
    <p>If you'd like to dig deeper into the sales of a particular product (or group of products), you can select the products you'd like to view by clicking the drop-down filter from the top of the page.</p>
    <p>You can also toggle data for your product selections by clicking "Toggle selected" at the bottom of the filter list.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/62469212c1e53608cf9ef35d/file-Hq8M9GB3iH.png" %></figure>
    <h3>Selecting the date range</h3>
    <p>By default, analytics shows data from the past month; however, it's often necessary to view sales data over a more extended period. From the date picker in the top right, you can access any range of data you'd like to view, going back to your first sale.</p>
    <p>The data in the sales chart, referrer list, and location will be updated to reflect the period you choose.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/64bf9636a9d61472afe09529/file-5EEqngjHHA.png" %></figure>
    <p>You can also toggle between daily and monthly views to get the best of both analytic worlds: the close-up and the big picture.</p>
    <h3>Sales chart</h3>
    <p>Sales data for the period you've selected is shown as a combined graph. Each bar represents a single day. The light grey section of the bar represents the number of views.</p>
    <p>A line graph of sales volume is overlaid on top to give you a sense of sales volume relative to your views.</p>
    <p>Hover on any day to see more information about the sales, views, and absolute sales value in dollars.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/6246932342ba434a7afe2278/file-V5ESJfaXNp.png" %></figure>
    <h3 id="Referrers-WBsBP">Referrers</h3>
    <p>A referrer is the original source of a visit to your product. </p>
    <ul>
      <li>If you share a link to your product on Twitter and a customer follows the link to your product page, the referrer would be Twitter. Similarly, </li>
      <li>If someone searches for your product on Google and then visits your product page, the referrer will be Google.For sales made through Gumroad Discover, the referrer will be listed as "Recommended by Gumroad."</li>
      <li>If the referrer is just "Gumroad," this indicates that the customer first visited another page on Gumroad (not via Discover) before viewing your product.</li>
    </ul>
    <p>All referrers to the selected product are shown in the referrers list. The number of views, purchases, and conversion percentage, as well as the absolute dollar amount of sales for each referrer, are shown here. You can sort this list by any of these metrics.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/624693d2c1688a6d26a7c11f/file-Zfg8pFmBDi.png" %></figure>
    <h3 id="Locations-PoKPf">Locations</h3>
    <p>To help you learn about your following, we show the approximate location of your customers in a table based on the IP of the purchase.</p>
    <p>You can see the views, the number of sales, and the dollar amount sold from each location in the country. You can also sort the table based on those columns.</p>
    <p>If you're in the U.S., we also allow you to switch to the 'United States' view from the 'World' view by clicking on the drop-down on the right. This will enable you to see the breakdown from the different states. At this time, we only show state-specific sales in the United States.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/624694c3c1e53608cf9ef364/file-ojfF44iVNk.gif" %></figure>
    <h3 id="sales-csv">Exporting a customer sales CSV</h3>
    <p>You can download a CSV file of your customers and their purchases within a selected date range from the <a href="https://app.gumroad.com/customers">Sales tab</a>. This CSV will cover all sales of your products within the date range, or you can choose to download information about a single product. If the CSV is not immediately available for download, it will be emailed to you. </p>
    <p>Note: Your Gumroad Analytics and Customers dashboards use your local time zone to display and filter orders. However, your CSV uses UTC time (Coordinated Universal Time). We recommend that you add a one-day buffer to the beginning and end of your date range before exporting to ensure that you capture all orders.</p>
    <h3>All products</h3>
    <p>For information on all sales within a specific date range, click the  <strong>Export</strong> icon, select a <strong>Date Range</strong>, and click <strong>Download</strong>.  </p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/657b073fe020fe4b2fcaef56/file-MEKHrXJBg4.png" %></figure>
    <h3>For a single product</h3>
    <p>Click the <strong>Filter</strong> icon to select the product you want to download information for. </p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/657b0a35e020fe4b2fcaef59/file-cF8gc9ssWx.png" %></figure>
    <p>Now click the <strong>Export</strong> icon, select a <strong>Date Range</strong>, and <strong>Download</strong>. </p>
    <h3 id="How-to-read-your-CSV-vLST9">How to read your CSV</h3>
    <table>
      <thead>
        <tr>
          <th><strong>Header</strong></th>
          <th><strong>Description</strong></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td> Purchase ID </td>
          <td> The randomized ID that Gumroad gives to every purchase </td>
        </tr>
        <tr>
          <td> Item Name </td>
          <td> The name you gave your product </td>
        </tr>
        <tr>
          <td> Buyer Name </td>
          <td> The name entered by your customer during checkout, if required </td>
        </tr>
        <tr>
          <td> Purchase email </td>
          <td> The email address used on the purchase </td>
        </tr>
        <tr>
          <td> Buyer Email </td>
          <td> If the customer was logged into a Gumroad account at checkout, this is that email address </td>
        </tr>
        <tr>
          <td> Do not contact </td>
          <td> If  <strong>0</strong> = You may email the buyer updates. <br>
            If  <strong>1</strong> = The buyer has opted out of communications. We will automatically remove the buyer from communications sent via your Gumroad customers tab. However, if you import this list to your own email provider, please remove these buyers. </td>
        </tr>
        <tr>
          <td> Purchase Date </td>
          <td> The date of purchase, in UTC (Coordinated Universal Time). For example, Eastern Standard Time (EST) is 5 hours behind Coordinated Universal Time (UTC−05:00). This means that the day ends at 7pm EST.  <br>
            <br>
            <a href="http://www.google.com/url?q=http%3A%2F%2Fen.wikipedia.org%2Fwiki%2FList_of_UTC_time_offsets&amp;sa=D&amp;sntz=1&amp;usg=AFQjCNGNsvr1UbX56-4bM5LbuXj7iLOKFQ">List of all time zones relative to UTC</a><br>
            <br>
            <em>*IMPORTANT: Your Customer and Analytics tab use your local time zone. This can sometimes make it seem like there are discrepancies between your dashboard and the CSV. To make sure you are capturing all your orders in the CSV, add a one-day buffer to the beginning and end of your date range.</em></td>
        </tr>
        <tr>
          <td> Purchase Time (UTC timezone) </td>
          <td> The exact time of purchase, in UTC (Coordinated Universal Time) </td>
        </tr>
        <tr>
          <td> Subtotal ($) </td>
          <td> The amount that your customer was set to pay before sales tax (sales tax does NOT mean VAT) </td>
        </tr>
        <tr>
          <td> Taxes ($) </td>
          <td> If US Sales tax calculation is enabled, the amount that you should set aside for remittance to the US government. For more information see our <a href="11-us-sales-tax-on-gumroad.html">help article on sales tax.</a></td>
        </tr>
        <tr>
          <td> Shipping ($) </td>
          <td> If you are charging shipping on physical products, this is where you can see what a customer paid. </td>
        </tr>
        <tr>
          <td> Sale Price ($) </td>
          <td> The final price your buyer paid for your product, including variants. </td>
        </tr>
        <tr>
          <td> Fees ($) </td>
          <td> This is how much Gumroad took out from the sale plus any additional fees from Apple/Google for in-app sales. You can read about fees <a href="66-gumroads-fees.html">here.</a></td>
        </tr>
        <tr>
          <td> Net Total ($) </td>
          <td> Your take-home pay (sale price - Gumroad's fees). </td>
        </tr>
        <tr>
          <td> Tax Included in Price? </td>
          <td> This applies to sales of physical products made within the US where sales tax is included. </td>
        </tr>
        <tr>
          <td> Street Address </td>
          <td> The buyer's street name and number for shipping, if required. </td>
        </tr>
        <tr>
          <td> City </td>
          <td> The buyer's city for shipping, if required. </td>
        </tr>
        <tr>
          <td> Zip Code </td>
          <td> The buyer's zip code/postal code for shipping, if required.  <br>
            If shipping fields are not enabled, Gumroad will automatically populate this field using geolocation data from the buyer's IP address. </td>
        </tr>
        <tr>
          <td> State </td>
          <td> Two letter state or provincial code, depending on the country. </td>
        </tr>
        <tr>
          <td> Country </td>
          <td> The buyer's country for shipping, if required.  <br>
            If shipping fields are not enabled, Gumroad will automatically populate this field using geolocation data from the buyer's IP address. </td>
        </tr>
        <tr>
          <td> Referrer </td>
          <td> The domain that referred the buyer to your product (i.e. <a href="https://www.facebook.com/">https://www.facebook.com</a>, <a href="https://mywebsite.com/">https://mywebsite.com</a>). <br>
            If the buyer typed the URL directly into their browser's address bar, have the URL bookmarked, are using certain email clients, or have certain security software installed, the referrer will be "direct." </td>
        </tr>
        <tr>
          <td> In-App Purchase? </td>
          <td> "1" if the purchase was made on the Gumroad mobile app, otherwise "0". </td>
        </tr>
        <tr>
          <td> Refunded? </td>
          <td> If "0" = No refund has been issued for this purchase  <br>
            If "1" = A refund has been issued. If your product is a physical good, do not ship the item to your buyer. </td>
        </tr>
        <tr>
          <td> Partial Refund ($) </td>
          <td> If you have issued a partial refund to a customer, that amount will show up here. </td>
        </tr>
        <tr>
          <td> Fully Refunded? </td>
          <td> 1 = A full refund has been issued, a 0 = No refund. </td>
        </tr>
        <tr>
          <td> Disputed? </td>
          <td> If "0" = No chargeback has been issued.  <br>
            If "1" = The buyer has contacted their bank and requested a chargeback for their purchase. Gumroad covers all chargeback fees for you. If your product is a physical good, do not ship the item to your buyer. </td>
        </tr>
        <tr>
          <td> Dispute won? </td>
          <td> If "0" = We did not win the dispute (if a dispute took place.)  <br>
            If "1" = We won the dispute and you've received the money back. </td>
        </tr>
        <tr>
          <td> Variants </td>
          <td> If there are variants associated with this product, they will displayed here in the format (variant 1, variant 2, variant 3). Otherwise this field will be blank. </td>
        </tr>
        <tr>
          <td> Discount Code </td>
          <td> If an offer code was used, it will be displayed here. Otherwise this field will be blank. </td>
        </tr>
        <tr>
          <td> Recurring Charge? </td>
          <td> If "0" = The item is a one-time purchase, or the  <strong>initial charge</strong> for a subscription product. <br>
            If "1" = This is an automated recurring charge for a subscription product. Note that each subsequent charge will appear on its own line.  <br>
            For more information, see our <a href="https://help.gumroad.com/article/83-subscription-products">help center article on subscriptions.</a></td>
        </tr>
        <tr>
          <td> Free trial purchase? </td>
          <td> 1 = This is a free trial sale where customers only authorize their payment method. The charge goes through only at the end of the trial period. <br>
            0 = Not a free trial. The customer was charged at the time of sale. <br>
            For more information, see our <a href="82-membership-products.html#FreeTrials">help article on free trials</a>. </td>
        </tr>
        <tr>
          <td> Pre-Order Authorization? </td>
          <td> If "0" = The item was not a pre-order and was paid for at checkout.  <br>
            If "1" = The item is a pre-order, and the credit card has been authorized. This is  <strong>not</strong> a real charge. It is just an authorization charge.  Note: The pre-orders feature has been deprecated. </td>
        </tr>
        <tr>
          <td> Product ID </td>
          <td> The product's unique permalink (i.e. "drIB").  <br>
            The unique permalink forms your product's URL, such as: <a href="https://gumroad.com/l/drIB.">https://gumroad.com/l/drIB.</a></td>
        </tr>
        <tr>
          <td> Order Number </td>
          <td> The number used to identify an order when using <a href="https://www.gumroad.com/ping">Gumroad Ping.</a></td>
        </tr>
        <tr>
          <td> Pre-Order Authorization time (UTC timezone) </td>
          <td> If a pre-order, this is the time that your customer pre-ordered the product </td>
        </tr>
        <tr>
          <td> Custom Fields </td>
          <td> If you have custom fields on your product, the information entered in them will show up here. More on custom fields <a href="101-designing-your-product-page.html#custom-fields">here</a>. </td>
        </tr>
        <tr>
          <td> Item Price ($) </td>
          <td> This is the absolute price of the sale relative to the base price. So, if you see a negative number here, that's the amount that a customer received through a discount code. </td>
        </tr>
        <tr>
          <td> Variants Price ($) </td>
          <td> If a customer chooses to buy a product with a variant, this is the amount that the product costs with that variant. </td>
        </tr>
        <tr>
          <td> Imported Customer? </td>
          <td> This is not a feature of Gumroad anymore, but if you have had Gumroad for a long time, 1 = an imported customer, 0 = organic customer. </td>
        </tr>
        <tr>
          <td> Giftee email </td>
          <td> If the purchase was made as a gift, this is the email address of the recipient. </td>
        </tr>
        <tr>
          <td> SKU ID </td>
          <td> If you are selling physical products, this is the SKU that you would use for tracking/shipping </td>
        </tr>
        <tr>
          <td> Quantity </td>
          <td> If you are selling physical products or products that can be purchased in quantities, this is where you'll see the quantity. </td>
        </tr>
        <tr>
          <td> Recurrence </td>
          <td> If the sale was of a subscription product, the recurrence will show up here as "Monthly," "Quarterly, " "Biannually," or "Annually" </td>
        </tr>
        <tr>
          <td> Affiliate </td>
          <td> If the sale was made through an affiliate link, you will see the affiliate's email address here. </td>
        </tr>
        <tr>
          <td> Affiliate commission ($) </td>
          <td> The amount that the affiliate earned on this payment. These will show up in your balance page as negative credits. </td>
        </tr>
        <tr>
          <td> Discover? </td>
          <td> If the sale was made through the Gumroad Discover feature, that will be indicated in this column as a "1" </td>
        </tr>
        <tr>
          <td> Subscription Ended Date </td>
          <td> If the customer cancelled their subscription, this will show the final day they had access to the subscription. Organize this column as "Descending" to find all of your cancelled subscribers. </td>
        </tr>
        <tr>
          <td> Rating </td>
          <td> The number of stars that this customer gave to your product. </td>
        </tr>
        <tr>
          <td> Review </td>
          <td> The written review submitted by this customer on the purchase. </td>
        </tr>
        <tr>
          <td> License Key </td>
          <td> If you use license keys on your products, that information will show up here. </td>
        </tr>
        <tr>
          <td> Payment Type </td>
          <td> From this column, you can see whether a sale was made on PayPal or credit/debit card. </td>
        </tr>
        <tr>
          <td> PayPal Transaction ID </td>
          <td> If the purchase was made on PayPal, this is the transaction ID that you can use to look it up in your PayPal account. </td>
        </tr>
        <tr>
          <td> Paypal Fee Amount   </td>
          <td> Amount of fee collected by PayPal for payments through <a href="275-paypal-connect.html">PayPal Connect</a></td>
        </tr>
        <tr>
          <td> Paypal Fee Currency </td>
          <td> The currency in which you've received payments through sales made via <a href="275-paypal-connect.html">PayPal Connect</a></td>
        </tr>
        <tr>
          <td>Stripe Transaction ID</td>
          <td>The transaction ID if the sale was processed through <a href="330-stripe-connect.html">Stripe Connect</a></td>
        </tr>
        <tr>
          <td>Stripe Fee Amount</td>
          <td>Amount of fee collected by Stripe for payments through <a href="330-stripe-connect.html">Stripe Connect</a></td>
        </tr>
        <tr>
          <td>Stripe Fee Currency</td>
          <td>Currency in which Stripe collected its fee for payments through <a href="330-stripe-connect.html">Stripe Connect</a></td>
        </tr>
        <tr>
          <td> Purchasing Power Parity Discounted? </td>
          <td> 1 = PPP discount was applied on the sale<br>
            0 = No PPP discount was applied on the sale<br>
            For more information, see our <a href="327-purchasing-power-parity.html">help article on Purchasing Power Parity</a>.<br>
          </td>
        </tr>
        <tr>
          <td>Upsold?</td>
          <td>1 if the sale came from an <a href="331-creating-upsells.html">upsell</a>, otherwise 0.</td>
        </tr>
        <tr>
          <td>Sent Abandoned Cart Email?</td>
          <td>1 if an <a href="131-using-workflows-to-send-automated-updates.html#Send-abandoned-cart-emails-UYdro">abandoned cart email</a> was sent for that purchase's cart, otherwise 0.</td>
        </tr>
      </tbody>
    </table>
    <p>You can filter a specific column for a more granular view of your data. Whether you are using Excel, Google Sheets, or Numbers on Mac, you can use the same filtering techniques to single out a specific data point to find exactly what you're looking for.</p>
    <figure><%= image_tag "https://d33v4339jhl8k0.cloudfront.net/docs/assets/5c4657ad2c7d3a66e32d763f/images/64bd93a1a9d61472afe09371/file-8LLEcO7kC9.gif" %></figure>
    <h3 id="utm-link-tracking">UTM Link Tracking</h3>
    <p>UTM link tracking allows you to track where your customers are coming from and measure the effectiveness of your marketing campaigns.</p>
    <h3>Enabling UTM Link Tracking</h3>
    <p>Creators can enable UTM link tracking in Gumroad by following these steps:</p>
    <ol>
      <li>Go to <a href="https://gumroad.com/dashboard/utm_links">Analytics → Links</a>.</li>
      <li>Click "Create link" and enter a "Destination" such as their Profile page, Subscribe page, a product page, or a post page.</li>
      <li>Add UTM parameters like Source, Medium, Campaign, etc., to track visitor origins.</li>
      <li>Once the UTM link is created, copy either the Short link or UTM link from the UTM link dashboard and share it with their audience.</li>
      <li>When someone clicks the UTM link, Gumroad records a unique click, and if a purchase is made within 7 days, it is attributed to that UTM link.</li>
      <li>To analyze performance, click on a UTM link in your dashboard to see Clicks, Sales, Revenue, and Conversion rate.</li>
    </ol>
    <h3>Important Notes on UTM Links</h3>
    <ul>
      <li>If the destination is a specific product page, only purchases of that product will be attributed to the UTM link.</li>
      <li>If the destination is a profile page, subscribe page, or a post page, purchases of any products will be attributed, regardless of which product is purchased.</li>
    </ul>
  </div>
  <div>
    <h3>Related Articles</h3>
    <ul>
      <li><a href="174-third-party-analytics.html"><span>Third-party analytics</span></a></li>
      <li><a href="177-the-gumroad-dashboard-app.html"><span>Gumroad mobile app</span></a></li>
    </ul>
  </div>
</div>
