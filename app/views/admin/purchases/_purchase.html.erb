<div class="card">
  <div style="display: grid; gap: var(--spacer-2)">
    <h2 class="purchase-title">
      <%= link_to(purchase.formatted_display_price, admin_purchase_path(purchase)) %>
      <%= " + #{purchase.formatted_gumroad_tax_amount} VAT" if purchase.gumroad_responsible_for_tax? %>
      <%= " for " %>
      <%= link_to_unless_current(h(purchase.link.name), admin_link_url(purchase.link), title: purchase.link.id) %>
      <%= purchase.variants_list %>
      <%= link_to purchase.link.long_url, class: "show" do %>
        <span class="icon icon-arrow-up-right-square"></span>
      <% end %>
    </h2>
    <ul class="inline">
      <li><%= format_datetime_with_relative_tooltip(purchase.created_at) %></li>
      <li><%= link_to(purchase.email, admin_search_purchases_path(query: purchase.email)) %></li>
    </ul>
  </div>

  <hr>
  <div class="paragraphs">
    <h3>Info</h3>
    <dl>
      <% if purchase.seller.support_email.present? %>
        <dt>Seller support email</dt>
        <dd><%= copy_to_clipboard purchase.seller.support_email %></dd>
      <% end %>

      <dt>Seller email</dt>
      <dd><%= copy_to_clipboard purchase.seller.email %></dd>

      <% if purchase.merchant_account %>
        <dt>Merchant account</dt>
        <dd><%= link_to("#{purchase.merchant_account.id} – #{purchase.merchant_account.charge_processor_id&.capitalize}", admin_merchant_account_path(purchase.merchant_account)) %></dd>

        <dt>Funds held by</dt>
        <dd><%= purchase.merchant_account.holder_of_funds.capitalize %></dd>
      <% end %>

      <dt>Fee</dt>
      <dd><%= Money.new(purchase.fee_cents).format(symbol: true) %></dd>

      <% if purchase.tip.present? %>
        <dt>Tip</dt>
        <dd><%= Money.new(purchase.tip.value_usd_cents).format(symbol: true) %></dd>
      <% end %>

      <% if purchase.tax_cents > 0 %>
        <dt>Seller Tax</dt>
        <dd>
          <%= purchase.formatted_seller_tax_amount %>
          <%= purchase.link.is_price_tax_exclusive ? " (Exclusive)" : " (Inclusive)" %>
        </dd>
      <% end %>

      <% if purchase.gumroad_tax_cents > 0 %>
        <dt>Gumroad Collected Tax</dt>
        <dd><%= purchase.formatted_gumroad_tax_amount %></dd>
      <% end %>

      <% if purchase.shipping_cents > 0 %>
        <dt>Shipping Cost</dt>
        <dd><%= purchase.formatted_shipping_amount %></dd>
      <% end %>

      <% if purchase.affiliate.present? %>
        <dt>Affiliate</dt>
        <dd><%= purchase.formatted_affiliate_credit_amount %></dd>
      <% end %>

      <dt>Transaction Total</dt>
      <dd><%= purchase.formatted_total_transaction_amount %></dd>

      <dt><%= purchase.charge_processor_id&.capitalize %> transaction ID</dt>
      <dd>
        <%= link_to_processor(purchase.charge_processor_id, purchase.stripe_transaction_id, purchase.charged_using_gumroad_merchant_account?, target: "_blank") %>
        |
        <%= link_to(purchase.id, admin_purchase_path(purchase)) %>
      </dd>

      <dt>Order number</dt>
      <dd><%= purchase.external_id_numeric %></dd>

      <% if purchase.quantity > 1 %>
        <dt>Quantity</dt>
        <dd><%= purchase.quantity %></dd>
      <% end %>

      <dt>Status</dt>
      <dd>
        <%= purchase.purchase_state.capitalize %>
        <%= "(refunded)" if purchase.stripe_refunded %>
        <%= "(partially refunded)" if purchase.stripe_partially_refunded %>
        <%= "(chargeback)" if purchase.chargedback_not_reversed? %>
        <%= "(chargeback reversed)" if purchase.chargeback_reversed? %>
        <%= "(#{purchase.formatted_error_code})" if purchase.failed? %>
      </dd>

      <% if purchase.refunds.present? %>
        <dt>Refunds</dt>
        <dd>
          <ul>
          <% purchase.refunds.each do |refund| %>
            <% user = refund.user %>
            <li>
              Refunder:
              <%= user ? link_to(user.name.presence || "User #{user.id}", admin_user_url(user)) : "(unknown)" %>
            </li>
            <li>
              Refund Status:
              <%= refund.status.capitalize %>
            </li>
          <% end %>
          </ul>
        </dd>
      <% end %>

      <% if purchase.card_type.present? && purchase.card_visual.present? %>
        <dt>Card</dt>
        <dd>
          <%= link_to admin_search_purchases_path(query: purchase.stripe_fingerprint) do %>
            <%= purchase.card_type.upcase %>
            *#<%= purchase.card_visual.delete("*").delete(" ") %> <%= "(#{purchase.card_country})" if purchase.card_country.present? %>
          <% end %>
          <% if purchase.charge_processor_id == StripeChargeProcessor.charge_processor_id %>
            |
            <%= link_to_stripe_fingerprint_search(purchase.stripe_fingerprint, target: "_blank") %>
          <% end %>
        </dd>

        <dt>IP Address</dt>
        <dd><%= link_to purchase.ip_address, admin_search_purchases_path(query: purchase.ip_address) %></dd>

        <dt>IP Country</dt>
        <dd><%= purchase.ip_country %></dd>
      <% end %>

      <% if purchase.is_preorder_authorization? %>
        <dt>Pre-order Receipt</dt>
        <dd>
          <% email_info = CustomerEmailInfo.where(purchase: purchase, email_name: "preorder_receipt").last %>
          <%= email_info.try(:state).try(:capitalize) %>
          <%= "(on #{email_info.delivered_at})" if email_info.try(:delivered?) %>
          <%= "(on #{email_info.opened_at})" if email_info.try(:opened?) %>
        </dd>
      <% else %>
        <dt>Receipt</dt>
        <dd>
          <% email_info = CustomerEmailInfo.where(purchase: purchase, email_name: "receipt").last %>
          <%= email_info.try(:state).try(:capitalize) %>
          <%= "(on #{email_info.delivered_at})" if email_info.try(:delivered?) %>
          <%= "(on #{email_info.opened_at})" if email_info.try(:opened?) %>
        </dd>
      <% end %>

      <% if purchase.is_bundle_purchase? %>
        <% purchase.product_purchases.each do |product_purchase| %>
          <% if product_purchase.url_redirect.present? %>
            <dt><%= product_purchase.link.name %></dt>
            <dd>
              <%= link_to product_purchase.url_redirect.download_page_url, product_purchase.url_redirect.download_page_url %>
              (<%= product_purchase.url_redirect.uses %> uses)
            </dd>
          <% end %>
        <% end %>
      <% elsif purchase.url_redirect.present? %>
        <dt>URL redirect</dt>
        <dd>
          <%= link_to purchase.url_redirect.download_page_url, purchase.url_redirect.download_page_url %>
          (<%= purchase.url_redirect.uses %> uses)
        </dd>
      <% end %>

      <% if purchase.subscription.present? %>
        <dt>Manage Membership URL</dt>
        <dd>
          <%= link_to manage_subscription_url(purchase.subscription.external_id), manage_subscription_url(purchase.subscription.external_id), target: "_blank" %>
        </dd>
      <% end %>

      <% if purchase.offer_code.present? && !purchase.is_gift_sender_purchase %>
        <dt>Discount code</dt>
        <dd>
          <%= purchase.offer_code.code %>
          for <%= purchase.offer_code.displayed_amount_off(purchase.link.price_currency_type, with_symbol: true) %> off
        </dd>
      <% end %>

      <% if purchase.street_address.present? %>
        <dt>Shipping</dt>
        <dd>
          <%= purchase.full_name %>
          <%= purchase.street_address %>
          <%= "#{purchase.city}, #{purchase.state} #{purchase.zip_code}" %>
          <%= purchase.country %>
        </dd>
      <% end %>

      <% if purchase.custom_fields.present? %>
        <% purchase.custom_fields.each do |field| %>
          <dt><%= field[:name] %></dt>
          <dd>
            <%= field[:value] %>
            (custom field)
          </dd>
        <% end %>
      <% end %>

      <% if purchase.purchase_state == "preorder_authorization_successful" %>
        <%= admin_action label: "Cancel Pre-order", url: cancel_preorder_by_seller_path(purchase.external_id), loading: "Canceling...", done: "Cancelled!", confirm_message: "Are you sure you want to cancel this preorder?", success_message: "Cancelled!" %>
      <% end %>

      <% if purchase.subscription.present? %>
        <dt>Cancelled</dt>
        <dd>
          <% if purchase.subscription.cancelled_at.present? %>
            <%= icon_yes %>
            <%= "(on #{purchase.subscription.cancelled_at} by #{purchase.subscription.cancelled_by_buyer ? 'buyer' : 'seller'})" %>
          <% else %>
            <%= icon_no %>
          <% end %>
        </dd>

        <dt>Ended</dt>
        <dd>
          <% if purchase.subscription.ended_at.present? %>
            <%= icon_yes %>
            <%= "(on #{purchase.subscription.ended_at})" %>
          <% else %>
            <%= icon_no %>
          <% end %>
        </dd>

        <dt>Failed</dt>
        <dd>
          <% if purchase.subscription.failed_at.present? %>
            <%= icon_yes %>
            <%= "(on #{purchase.subscription.failed_at})" %>
          <% else %>
            <%= icon_no %>
          <% end %>
        </dd>
      <% end %>

      <% if purchase.license.present? %>
        <dt>License</dt>
        <dd><%= purchase.license.serial %></dd>
      <% end %>

      <% if purchase.affiliate.present? %>
        <dt>Affiliate</dt>
        <dd><%= link_to(purchase.affiliate.affiliate_user.form_email, admin_search_users_path(query: "#{purchase.affiliate.affiliate_user.form_email}")) %></dd>
      <% end %>

      <% if purchase.purchase_refund_policy.present? %>
        <dt>Refund Policy</dt>
        <dd>
          <strong><%= purchase.purchase_refund_policy.title %></strong>
          <% if purchase.purchase_refund_policy.fine_print.present? %>
            <br>
            <%= simple_format(purchase.purchase_refund_policy.fine_print) %>
          <% end %>
          <% if purchase.purchase_refund_policy.max_refund_period_in_days.present? %>
            <br>
            <small>Max refund period: <%= purchase.purchase_refund_policy.max_refund_period_in_days %> days</small>
          <% end %>
        </dd>
      <% end %>

      <dt>Can email</dt>
      <dd aria-label="Can email">
        <%= purchase.can_contact? ? icon_yes : icon_no %>
      </dd>

      <% if purchase.is_gift_sender_purchase %>
        <dt>Receiver purchase id</dt>
        <dd><%= link_to(purchase.gift.giftee_purchase_id, admin_purchase_path(purchase.gift.giftee_purchase_id)) %></dd>
      <% end %>

      <% if purchase.is_gift_receiver_purchase %>
        <dt>Sender purchase id</dt>
        <dd><%= link_to(purchase.gift.gifter_purchase_id, admin_purchase_path(purchase.gift.gifter_purchase_id)) %></dd>
      <% end %>
    </dl>
  </div>

  <% if purchase.is_gift_sender_purchase %>
    <hr>
    <details>
      <summary>
        <h3>Gift Sender Info</h3>
      </summary>
      <dl>
        <dt>For</dt>
        <dd><%= purchase.giftee_email %></dd>

        <dt>Note</dt>
        <dd><%= purchase.gift_note %></dd>

        <dt>Receiver purchase id</dt>
        <dd><%= link_to(purchase.gift.giftee_purchase_id, admin_purchase_path(purchase.gift.giftee_purchase_id)) %></dd>
      </dl>
    </details>

    <hr>
    <details>
      <summary>
        <h3>Edit giftee email</h3>
      </summary>
      <%= form_for(:update_giftee_email, url: update_giftee_email_admin_purchase_path(purchase.id), html: { class: "input-with-button" }) do |f| %>
        <%= f.text_field :giftee_email, placeholder: "Enter new giftee email" %>
        <%= f.button "Update", class: "button" %>
      <% end %>
    </details>
  <% end %>

  <% if purchase.is_gift_receiver_purchase %>
    <hr>
    <details>
      <summary>
        <h3>Gift Receiver Info</h3>
      </summary>
      <dl>
        <dt>From</dt>
        <dd><%= purchase.gifter_email %></dd>

        <dt>Note</dt>
        <dd><%= purchase.gift_note %></dd>

        <dt>Sender purchase id</dt>
        <dd><%= link_to(purchase.gift.gifter_purchase_id, admin_purchase_path(purchase.gift.gifter_purchase_id)) %></dd>
      </dl>
    </details>
  <% end %>

  <% if purchase.successful? || purchase.preorder_authorization_successful? || purchase.is_free_trial_purchase? %>
    <hr>
    <details>
      <summary>
        <h3>Resend receipt</h3>
      </summary>
      <%= react_component "AdminResendReceiptForm", props: { purchase_id: purchase.id, email: purchase.email }, prerender: true %>
    </details>
  <% end %>

  <hr>
  <div class="button-group">
    <% if purchase.can_force_update? || purchase.failed? %>
      <%= admin_action label: "Sync with Stripe/PayPal", url: sync_status_with_charge_processor_admin_purchase_path(purchase), loading: "syncing...", done: "synced!", confirm_message: "Are you sure you want to sync this purchase's state with Stripe/PayPal?", success_message: "synced!" %>
    <% end %>
    <% if purchase.successful? && !purchase.stripe_refunded %>
      <%= admin_action label: "Refund", url: refund_admin_purchase_path(purchase), loading: "Refunding...", done: "Refunded!", confirm_message: "Are you sure you want to refund this purchase?", success_message: "Refunded!" %>
      <%= admin_action label: "Refund for Fraud", url: refund_for_fraud_admin_purchase_path(purchase), loading: "Refunding...", done: "Refunded!", confirm_message: "Are you sure you want to refund this purchase for fraud?", success_message: "Refunded!" %>
      <%= admin_action label: "Refund Card for Fraud", url: refund_admin_cards_path(stripe_fingerprint: purchase.stripe_fingerprint), loading: "Refunding...", done: "Refunding purchases!", confirm_message: "Are you sure you want to Mass-refund for fraud all purchases associated with this purchase's card?", success_message: "Refunding purchases!" %>
    <% end %>
    <% if purchase.subscription.present? && !purchase.subscription.cancelled_or_failed? && !purchase.subscription.ended? %>
      <%= admin_action label: "Cancel subscription for buyer", url: cancel_subscription_admin_purchase_path(purchase, by_seller: false), loading: "Canceling...", done: "Canceled!", confirm_message: "Are you sure you want to cancel this subscription on behalf of the buyer?", success_message: "Canceled!" %>
      <%= admin_action label: "Cancel subscription for seller", url: cancel_subscription_admin_purchase_path(purchase, by_seller: true), loading: "Canceling...", done: "Canceled!", confirm_message: "Are you sure you want to cancel this subscription on behalf of the seller?", success_message: "Canceled!" %>
    <% end %>
    <% if purchase.buyer_blocked? %>
      <%= admin_action label: "Unblock buyer", url: unblock_buyer_admin_purchase_path(purchase), loading: "Unblocking buyer...", done: "Buyer unblocked!", success_message: "Buyer unblocked!" %>
    <% else %>
      <%= admin_action label: "Block buyer", url: block_buyer_admin_purchase_path(purchase), loading: "Blocking buyer...", done: "Buyer blocked!", confirm_message: "This will fully block this buyer's emails, GUID, and IP addresses. Proceed?", success_message: "Buyer blocked!" %>
    <% end %>
    <% if purchase.successful? %>
      <%= link_to "Go to Receipt", receipt_purchase_path(purchase.external_id), target: "_blank", class: "button small" %>
    <% end %>
  </div>

  <hr>
  <div class="paragraphs">
    <h3>Comments</h3>
    <%= render("admin/comments/form", commentable: purchase) %>
    <%= render("admin/comments/index", commentable: purchase) %>
  </div>

  <hr>
  <dl>
    <dt>Updated</dt>
    <dd><%= format_datetime_with_relative_tooltip(purchase.updated_at) %></dd>
    <dt>Deleted</dt>
    <dd><%= format_datetime_with_relative_tooltip(purchase.deleted_at, placeholder: icon_no) %></dd>
  </dl>
</div>
